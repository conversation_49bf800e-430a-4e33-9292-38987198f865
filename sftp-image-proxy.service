[Unit]
Description=SFTP Image Proxy Service for SpaceData Platform
Documentation=https://github.com/your-repo/spacedata-platform
After=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/var/www/spacedata-platform
Environment=NODE_ENV=production
Environment=IMAGE_PROXY_PORT=3002
ExecStart=/usr/bin/node sftp-image-proxy-server.prod.js
ExecReload=/bin/kill -HUP $MAINPID
KillMode=mixed
KillSignal=SIGINT
TimeoutStopSec=5
Restart=always
RestartSec=10
StartLimitInterval=60s
StartLimitBurst=3

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/www/spacedata-platform

# 日志设置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=sftp-image-proxy

[Install]
WantedBy=multi-user.target
