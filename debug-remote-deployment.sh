#!/bin/bash

# 远程部署图片代理问题诊断脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔍 远程部署图片代理问题诊断${NC}"
echo "=================================="

# 1. 检查SFTP图片代理服务状态
echo -e "\n${BLUE}1. 检查SFTP图片代理服务状态${NC}"
echo "----------------------------------------"

# 检查PM2服务
if command -v pm2 &> /dev/null; then
    echo "📊 PM2服务状态:"
    pm2 list | grep -E "(sftp|image|proxy)" || echo "未找到相关PM2进程"
    
    if pm2 list | grep -q "sftp-image-proxy"; then
        echo -e "\n📋 详细服务信息:"
        pm2 show sftp-image-proxy
        
        echo -e "\n📝 最近日志:"
        pm2 logs sftp-image-proxy --lines 20
    fi
else
    echo "PM2未安装，检查手动启动的进程..."
    
    # 检查手动启动的进程
    if [ -f ".sftp-proxy.pid" ]; then
        PID=$(cat .sftp-proxy.pid)
        if kill -0 "$PID" 2>/dev/null; then
            echo -e "${GREEN}✅ 图片代理进程运行中 (PID: $PID)${NC}"
        else
            echo -e "${RED}❌ 图片代理进程未运行${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  未找到PID文件${NC}"
    fi
fi

# 2. 检查端口监听状态
echo -e "\n${BLUE}2. 检查端口监听状态${NC}"
echo "----------------------------------------"

PORTS=(3002 3003 3004)
for PORT in "${PORTS[@]}"; do
    if netstat -tlnp 2>/dev/null | grep -q ":$PORT "; then
        echo -e "${GREEN}✅ 端口 $PORT 正在监听${NC}"
        netstat -tlnp 2>/dev/null | grep ":$PORT "
    else
        echo -e "${RED}❌ 端口 $PORT 未在监听${NC}"
    fi
done

# 3. 测试本地健康检查
echo -e "\n${BLUE}3. 测试本地健康检查${NC}"
echo "----------------------------------------"

for PORT in "${PORTS[@]}"; do
    echo "测试端口 $PORT..."
    if curl -s --connect-timeout 5 "http://localhost:$PORT/health" > /dev/null; then
        echo -e "${GREEN}✅ 端口 $PORT 健康检查通过${NC}"
        echo "响应内容:"
        curl -s "http://localhost:$PORT/health" | python3 -m json.tool 2>/dev/null || curl -s "http://localhost:$PORT/health"
        echo ""
    else
        echo -e "${RED}❌ 端口 $PORT 健康检查失败${NC}"
    fi
done

# 4. 检查SFTP连接
echo -e "\n${BLUE}4. 检查SFTP连接${NC}"
echo "----------------------------------------"

if [ -f "test-sftp-connection.js" ]; then
    echo "运行SFTP连接测试..."
    timeout 30 node test-sftp-connection.js || echo "SFTP连接测试超时或失败"
else
    echo "创建简单SFTP测试..."
    cat > temp-sftp-test.js << 'EOF'
const SftpClient = require('ssh2-sftp-client');

async function testSFTP() {
    const sftp = new SftpClient();
    try {
        console.log('🔌 测试SFTP连接...');
        await sftp.connect({
            host: '**************',
            port: 22,
            username: 'yulingjing',
            password: 'readnewsjpg123',
            readyTimeout: 10000
        });
        console.log('✅ SFTP连接成功');
        
        const list = await sftp.list('./');
        console.log(`📁 找到 ${list.length} 个文件/目录`);
        
        await sftp.end();
    } catch (error) {
        console.error('❌ SFTP连接失败:', error.message);
    }
}

testSFTP();
EOF
    
    if command -v node &> /dev/null; then
        timeout 30 node temp-sftp-test.js || echo "SFTP测试失败"
        rm -f temp-sftp-test.js
    else
        echo "Node.js未安装，无法测试SFTP连接"
    fi
fi

# 5. 检查防火墙和网络
echo -e "\n${BLUE}5. 检查防火墙和网络${NC}"
echo "----------------------------------------"

# 检查防火墙状态
if command -v ufw &> /dev/null; then
    echo "🛡️  UFW防火墙状态:"
    sudo ufw status || echo "无法获取UFW状态"
elif command -v firewall-cmd &> /dev/null; then
    echo "🛡️  Firewalld状态:"
    sudo firewall-cmd --list-all || echo "无法获取Firewalld状态"
else
    echo "未检测到常见防火墙工具"
fi

# 检查iptables
echo -e "\n🔗 iptables规则 (INPUT链):"
sudo iptables -L INPUT -n --line-numbers | head -10 || echo "无法获取iptables规则"

# 6. 检查配置文件
echo -e "\n${BLUE}6. 检查配置文件${NC}"
echo "----------------------------------------"

# 检查前端图片服务配置
if [ -f "frontend/src/services/imageService.ts" ]; then
    echo "🖼️  前端图片服务配置:"
    grep -n "IMAGE_PROXY_BASE_URL\|ENABLE_FTP_PROXY" frontend/src/services/imageService.ts || echo "未找到相关配置"
fi

# 检查API配置
if [ -f "frontend/src/services/api.ts" ]; then
    echo -e "\n🌐 API配置:"
    grep -n "API_BASE_URL" frontend/src/services/api.ts || echo "未找到API配置"
fi

# 检查生产环境代理配置
if [ -f "sftp-image-proxy-server.prod.js" ]; then
    echo -e "\n⚙️  生产环境代理配置:"
    grep -n "PORT\|host\|username" sftp-image-proxy-server.prod.js | head -5 || echo "未找到配置信息"
fi

# 7. 检查日志文件
echo -e "\n${BLUE}7. 检查日志文件${NC}"
echo "----------------------------------------"

if [ -f "sftp-proxy.log" ]; then
    echo "📝 代理服务日志 (最后20行):"
    tail -20 sftp-proxy.log
else
    echo "未找到代理服务日志文件"
fi

# 8. 网络连通性测试
echo -e "\n${BLUE}8. 网络连通性测试${NC}"
echo "----------------------------------------"

echo "🌐 测试SFTP服务器连通性:"
ping -c 3 ************** || echo "SFTP服务器不可达"

echo -e "\n🔌 测试SFTP端口:"
nc -z -v ************** 22 || echo "SFTP端口不可达"

# 9. 系统资源检查
echo -e "\n${BLUE}9. 系统资源检查${NC}"
echo "----------------------------------------"

echo "💾 内存使用:"
free -h

echo -e "\n💿 磁盘使用:"
df -h . | head -2

echo -e "\n⚡ 系统负载:"
uptime

# 10. 生成修复建议
echo -e "\n${BLUE}10. 修复建议${NC}"
echo "----------------------------------------"

echo "基于诊断结果，请尝试以下修复步骤:"
echo ""
echo "1. 如果图片代理服务未运行:"
echo "   ./manage-services.sh start"
echo ""
echo "2. 如果端口被占用:"
echo "   sudo lsof -i :3002"
echo "   sudo kill -9 <PID>"
echo ""
echo "3. 如果SFTP连接失败:"
echo "   检查网络连接和防火墙设置"
echo "   确认SFTP服务器凭据正确"
echo ""
echo "4. 如果防火墙阻止连接:"
echo "   sudo ufw allow 3002"
echo "   sudo firewall-cmd --add-port=3002/tcp --permanent"
echo ""
echo "5. 重新部署:"
echo "   ./fix-api-config-with-proxy.sh"
echo ""
echo "6. 查看详细日志:"
echo "   pm2 logs sftp-image-proxy"
echo "   tail -f sftp-proxy.log"

echo -e "\n${GREEN}✅ 诊断完成！${NC}"
echo "请根据上述结果进行相应的修复操作。"
