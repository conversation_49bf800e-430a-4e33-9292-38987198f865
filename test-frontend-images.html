<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFTP图片代理测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-image {
            max-width: 200px;
            height: auto;
            border: 1px solid #ccc;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ SFTP图片代理功能测试</h1>
        
        <div class="test-section">
            <h3>📊 代理服务器状态</h3>
            <button onclick="checkProxyHealth()">检查健康状态</button>
            <button onclick="checkCacheStatus()">检查缓存状态</button>
            <div id="proxy-status"></div>
        </div>

        <div class="test-section">
            <h3>🧪 图片加载测试</h3>
            <p>测试SFTP代理服务器的图片加载功能：</p>
            
            <div>
                <h4>测试图片 1: test-news-image.jpg</h4>
                <img id="test-image-1" class="test-image" 
                     src="http://localhost:3002/image?path=test-news-image.jpg" 
                     alt="测试图片1"
                     onload="onImageLoad(this, 'test-news-image.jpg')"
                     onerror="onImageError(this, 'test-news-image.jpg')">
                <div id="status-1"></div>
            </div>

            <div>
                <h4>测试图片 2: 不存在的图片</h4>
                <img id="test-image-2" class="test-image" 
                     src="http://localhost:3002/image?path=nonexistent.jpg" 
                     alt="测试图片2"
                     onload="onImageLoad(this, 'nonexistent.jpg')"
                     onerror="onImageError(this, 'nonexistent.jpg')">
                <div id="status-2"></div>
            </div>

            <div>
                <h4>测试图片 3: 默认回退图片</h4>
                <img id="test-image-3" class="test-image" 
                     src="https://images.unsplash.com/photo-1451187580459-43490279c0fa?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=200&q=80" 
                     alt="默认太空图片"
                     onload="onImageLoad(this, '默认太空图片')"
                     onerror="onImageError(this, '默认太空图片')">
                <div id="status-3"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>📝 测试日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="log" class="log"></div>
        </div>

        <div class="test-section">
            <h3>🔧 手动测试</h3>
            <p>输入图片路径进行测试：</p>
            <input type="text" id="manual-path" placeholder="例如: test-news-image.jpg" style="width: 300px; padding: 5px;">
            <button onclick="testManualPath()">测试图片</button>
            <div id="manual-result"></div>
            <img id="manual-image" class="test-image" style="display: none;">
        </div>
    </div>

    <script>
        let logContainer = document.getElementById('log');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logEntry.className = type;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[SFTP Test] ${message}`);
        }

        function clearLog() {
            logContainer.innerHTML = '';
        }

        function onImageLoad(img, path) {
            const statusId = img.id.replace('test-image', 'status');
            const statusDiv = document.getElementById(statusId);
            if (statusDiv) {
                statusDiv.innerHTML = '<span class="status success">✅ 加载成功</span>';
            }
            log(`图片加载成功: ${path}`, 'success');
        }

        function onImageError(img, path) {
            const statusId = img.id.replace('test-image', 'status');
            const statusDiv = document.getElementById(statusId);
            if (statusDiv) {
                statusDiv.innerHTML = '<span class="status error">❌ 加载失败</span>';
            }
            log(`图片加载失败: ${path}`, 'error');
            
            // 对于SFTP代理图片，尝试回退到默认图片
            if (img.src.includes('localhost:3002')) {
                log(`尝试回退到默认图片: ${path}`, 'info');
                img.src = 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=200&q=80';
            }
        }

        async function checkProxyHealth() {
            try {
                log('检查代理服务器健康状态...', 'info');
                const response = await fetch('http://localhost:3002/health');
                const data = await response.json();
                
                document.getElementById('proxy-status').innerHTML = `
                    <div class="status success">✅ 代理服务器运行正常</div>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                log('代理服务器健康检查通过', 'success');
            } catch (error) {
                document.getElementById('proxy-status').innerHTML = `
                    <div class="status error">❌ 代理服务器不可用: ${error.message}</div>
                `;
                log(`代理服务器健康检查失败: ${error.message}`, 'error');
            }
        }

        async function checkCacheStatus() {
            try {
                log('检查缓存状态...', 'info');
                const response = await fetch('http://localhost:3002/cache/status');
                const data = await response.json();
                
                document.getElementById('proxy-status').innerHTML = `
                    <div class="status info">📊 缓存状态</div>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                log(`缓存状态: ${data.totalEntries} 个条目`, 'info');
            } catch (error) {
                log(`缓存状态检查失败: ${error.message}`, 'error');
            }
        }

        async function testManualPath() {
            const path = document.getElementById('manual-path').value.trim();
            if (!path) {
                alert('请输入图片路径');
                return;
            }

            log(`手动测试图片路径: ${path}`, 'info');
            
            const img = document.getElementById('manual-image');
            const result = document.getElementById('manual-result');
            
            img.style.display = 'none';
            result.innerHTML = '<div class="status info">⏳ 加载中...</div>';
            
            const testUrl = `http://localhost:3002/image?path=${encodeURIComponent(path)}`;
            
            img.onload = function() {
                result.innerHTML = '<div class="status success">✅ 图片加载成功</div>';
                img.style.display = 'block';
                log(`手动测试成功: ${path}`, 'success');
            };
            
            img.onerror = function() {
                result.innerHTML = '<div class="status error">❌ 图片加载失败</div>';
                img.style.display = 'none';
                log(`手动测试失败: ${path}`, 'error');
            };
            
            img.src = testUrl;
        }

        // 页面加载时自动检查代理服务器状态
        window.onload = function() {
            log('页面加载完成，开始测试...', 'info');
            checkProxyHealth();
        };
    </script>
</body>
</html>
