#!/usr/bin/env node

/**
 * SFTP连接测试脚本
 */

const SftpClient = require('ssh2-sftp-client');

// SFTP服务器配置
const SFTP_CONFIG = {
  host: '**************',
  port: 22,
  username: 'yulingjing',
  password: 'readnewsjpg123',
  readyTimeout: 20000,
  retries: 3
};

async function testSftpConnection() {
  const sftp = new SftpClient();
  
  console.log('🔍 开始测试SFTP连接...');
  console.log(`📡 服务器: ${SFTP_CONFIG.host}:${SFTP_CONFIG.port}`);
  console.log(`👤 用户: ${SFTP_CONFIG.username}`);
  
  try {
    console.log('\n⏳ 正在连接到SFTP服务器...');
    await sftp.connect(SFTP_CONFIG);
    console.log('✅ SFTP连接成功！');
    
    console.log('\n📁 获取根目录列表...');
    const list = await sftp.list('./');
    console.log(`📂 找到 ${list.length} 个文件/目录:`);
    
    list.slice(0, 10).forEach(item => {
      console.log(`   ${item.type === 'd' ? '📁' : '📄'} ${item.name} (${item.size} bytes)`);
    });
    
    // 测试常见的新闻图片路径
    const testPaths = [
      '/Spacehome/data/spacesecure/news_data/news_jpg',
      './Spacehome/data/spacesecure/news_data/news_jpg',
      'Spacehome/data/spacesecure/news_data/news_jpg',
      '/data/spacesecure/news_data/news_jpg',
      './data/spacesecure/news_data/news_jpg'
    ];
    
    console.log('\n🔍 测试常见图片目录路径...');
    for (const testPath of testPaths) {
      try {
        const pathList = await sftp.list(testPath);
        console.log(`✅ 找到路径: ${testPath} (${pathList.length} 个文件)`);
        
        // 显示前几个文件
        const imageFiles = pathList.filter(item => 
          item.name.toLowerCase().endsWith('.jpg') || 
          item.name.toLowerCase().endsWith('.png')
        ).slice(0, 3);
        
        if (imageFiles.length > 0) {
          console.log('   图片文件示例:');
          imageFiles.forEach(file => {
            console.log(`   📸 ${file.name} (${file.size} bytes)`);
          });
        }
        break; // 找到一个有效路径就停止
      } catch (pathError) {
        console.log(`❌ 路径不存在: ${testPath}`);
      }
    }
    
    await sftp.end();
    console.log('\n🎉 SFTP测试完成！');
    
  } catch (error) {
    console.error('\n❌ SFTP连接失败:', error.message);
    console.error('\n🔧 可能的解决方案:');
    console.error('   1. 检查网络连接');
    console.error('   2. 验证SFTP服务器地址和端口');
    console.error('   3. 确认用户名和密码正确');
    console.error('   4. 检查防火墙设置');
    
    if (error.code) {
      console.error(`\n错误代码: ${error.code}`);
    }
    
    return false;
  }
  
  return true;
}

// 运行测试
if (require.main === module) {
  testSftpConnection()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('测试脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { testSftpConnection, SFTP_CONFIG };
