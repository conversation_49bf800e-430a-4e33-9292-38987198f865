import React from 'react';
import styled from 'styled-components';
import { Calendar, ExternalLink } from 'lucide-react';

const Card = styled.div`
  display: flex;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(8px);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  
  &:hover {
    border-color: rgba(255, 255, 255, 0.2);
    background: rgba(0, 0, 0, 0.3);
  }
  
  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

const ImageContainer = styled.div`
  flex: 0 0 200px;
  position: relative;
  background-color: rgba(0, 0, 0, 0.15);
  
  @media (max-width: 768px) {
    height: 160px;
    flex-basis: 160px;
  }
`;

const Image = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const Content = styled.div`
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
`;

const Title = styled.a`
  font-size: 18px;
  font-weight: 500;
  color: #fff;
  margin-bottom: 8px;
  text-decoration: none;
  line-height: 1.3;
  
  &:hover {
    text-decoration: underline;
    color: #40a9ff;
  }
`;

const Meta = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  color: rgba(255, 255, 255, 0.5);
  font-size: 14px;
  margin-bottom: 12px;
`;

const Summary = styled.p`
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
  flex: 1;
`;

const TagsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: auto;
`;

const Tag = styled.span`
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.6);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
`;

interface NewsCardProps {
  id: string;
  title: string;
  date: string;
  summary: string;
  imageUrl: string;
  tags: string[];
  source: string;
  siteName?: string;
}

export function NewsCard({ id, title, date, summary, imageUrl, tags, source, siteName }: NewsCardProps) {
  // 处理图片加载失败
  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    e.currentTarget.src = '/assets/images/news-placeholder.jpg';
  };

  // 格式化日期
  const formatDate = (dateStr: string) => {
    try {
      const date = new Date(dateStr);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (e) {
      return dateStr;
    }
  };

  return (
    <Card>
      <ImageContainer>
        {imageUrl ? (
          <Image src={imageUrl} alt={title} onError={handleImageError} />
        ) : (
          <Image src="/assets/images/news-placeholder.jpg" alt={title} />
        )}
      </ImageContainer>

      <Content>
        <Title href={`/news/${id}`} target="_blank" rel="noopener noreferrer">{title}</Title>
        
        <Meta>
          <div className="flex items-center gap-1">
            <Calendar size={14} />
            <span>{formatDate(date)}</span>
          </div>
          
          {source && (
            <div className="flex items-center gap-1">
              <ExternalLink size={14} />
              <span>{source}</span>
            </div>
          )}
        </Meta>
        
        <Summary>{summary}</Summary>
        
        <TagsContainer>
          {siteName && (
            <Tag className="bg-blue-500/20 text-blue-400">{siteName}</Tag>
          )}
          
          {tags && tags.slice(0, 3).map((tag, index) => (
            <Tag key={index}>{tag}</Tag>
          ))}
        </TagsContainer>
      </Content>
    </Card>
  );
} 