import React from 'react';
import { Calendar } from 'lucide-react';
import { motion } from 'framer-motion';
import { NewsItem } from '../../types';

/**
 * 新闻卡片组件
 * 显示新闻的标题、摘要、日期、标签等信息
 */
export function NewsCard({ id, title, summary, date, imageUrl, tags, keyword }: NewsItem) {
  // 查找标题和摘要中包含的关键词
  const matchedKeywords = tags.filter(tag => 
    keyword && (
      title.toLowerCase().includes(tag.toLowerCase()) || 
      summary.toLowerCase().includes(tag.toLowerCase())
    )
  );

  return (
    <article className="
      relative overflow-hidden
      bg-white/5 hover:bg-white/10
      backdrop-blur-md
      rounded-xl
      transition-all duration-200
      group
    ">
      <a href={`/news/${id}`} target="_blank" rel="noopener noreferrer" className="block">
        <div className="flex items-start gap-6 p-6">
          {/* 新闻内容 */}
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-medium text-white mb-2 line-clamp-2">
              {title}
            </h3>
            <p className="text-white/70 mb-4 line-clamp-2">
              {summary}
            </p>
            <div className="flex flex-wrap gap-4">
              {/* 日期 */}
              <div className="flex items-center gap-2 text-white/50">
                <Calendar className="w-4 h-4" />
                <span className="text-sm">{date}</span>
              </div>
              {/* 标签 */}
              <div className="flex flex-wrap items-center gap-2">
                {tags.map((tag) => (
                  <span
                    key={tag}
                    className={`
                      px-2 py-1 text-xs rounded-full
                      ${matchedKeywords.includes(tag) 
                        ? 'bg-blue-500/20 text-blue-300' 
                        : 'bg-white/5 text-white/70'}
                    `}
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          </div>

          {/* 新闻图片 */}
          {imageUrl && (
            <div className="relative w-48 h-32 flex-shrink-0">
              <img
                src={imageUrl}
                alt={title}
                className="
                  w-full h-full object-cover
                  rounded-lg
                  transition-transform duration-200
                  group-hover:scale-105
                "
              />
            </div>
          )}
        </div>
      </a>
    </article>
  );
} 