import { Calendar, Circle } from 'lucide-react';
import { useSearchParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import { NewsItem } from '../../types';
import { createImageLoader } from '../../../../services/imageService';

export function NewsCard({
  id,
  title,
  summary,
  date,
  imageUrl,
  tags,
  themes_cn,
  isRead = true,
  matchedKeywords = [],
  originalData
}: NewsItem) {
  // 调试信息
  console.log(`[NewsCard] Rendering news item ${id}:`, { imageUrl, themes_cn, tags });

  // 使用themes_cn字段，如果没有则使用tags字段
  const displayTags = themes_cn || tags;
  const [searchParams] = useSearchParams();
  const currentType = searchParams.get('type') || 'latest';

  // 创建图片加载器，优先使用jpg_path，然后是imageUrl
  const { imageUrl: finalImageUrl, onError } = createImageLoader(
    originalData?.jpg_path || imageUrl
  );

  return (
    <motion.article
      whileHover={{ y: -2 }}
      className="
        bg-white/5 hover:bg-white/[0.07]
        backdrop-blur-sm
        border border-white/10
        rounded-xl
        overflow-hidden
        group
        transition-all duration-200
        relative
      "
    >
      {/* 未读标签 - 移到右上角并增加z-index确保在最上层 */}
      {!isRead && (
        <div className="absolute top-2 right-2 z-10 flex items-center gap-1.5 px-2 py-1 rounded-full bg-blue-500/20 shadow-md">
          <Circle className="w-2 h-2 fill-blue-400 text-blue-400" />
          <span className="text-xs text-blue-400">未读</span>
        </div>
      )}

      <a href={`/news/${id}?from=${currentType}`} target="_blank" rel="noopener noreferrer" className="block">
        {/* 响应式布局：移动端垂直，桌面端水平 */}
        <div className="flex flex-col md:flex-row gap-4 md:gap-6 p-4 md:p-6">
          {/* Image - 响应式尺寸 */}
          <div className="w-full md:w-48 h-48 md:h-32 flex-shrink-0 overflow-hidden rounded-lg">
            <img
              src={finalImageUrl}
              alt={title}
              onError={onError}
              className="
                w-full h-full object-cover
                transform group-hover:scale-105
                transition-transform duration-300
              "
            />
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <h2 className="text-lg md:text-xl font-medium text-white group-hover:text-blue-400 transition-colors mb-2 md:mb-3 leading-tight">
              {title}
            </h2>

            <p className="text-sm text-gray-300 line-clamp-2 md:line-clamp-2 mb-3 md:mb-4 leading-relaxed">
              {summary}
            </p>

            <div className="flex flex-col gap-2 md:gap-3">
              {/* 匹配的关键字 */}
              {matchedKeywords.length > 0 && (
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 text-sm">
                  <span className="text-gray-400 flex-shrink-0">命中关键字：</span>
                  <div className="flex flex-wrap gap-1 md:gap-2">
                    {matchedKeywords.map(keyword => (
                      <span
                        key={keyword}
                        className="
                          px-2 py-1 text-xs rounded-full
                          bg-green-500/10 text-green-400
                        "
                      >
                        {keyword}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* 主题标签 - 响应式布局 */}
              {Array.isArray(displayTags) && displayTags.length > 0 && (
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-2 md:mb-3">
                  <div className="flex items-center gap-2 flex-shrink-0">
                    <span className="text-gray-400 text-sm hidden sm:inline">标签：</span>
                  </div>
                  <div className="flex flex-wrap gap-1 md:gap-2">
                    {(() => {
                      // 处理标签，拆分包含"科普军事"的复合标签
                      const processedTags: string[] = [];
                      
                      displayTags.forEach(tag => {
                        // 先清理标签，去除多余的空格和标点符号
                        const cleanTag = tag.trim().replace(/^[,，\s]+|[,，\s]+$/g, '');
                        
                        if (!cleanTag) return; // 跳过空标签
                        
                        if (cleanTag.includes('科普') && cleanTag.includes('军事')) {
                          // 如果标签同时包含科普和军事，拆分为两个独立标签
                          processedTags.push('科普', '军事');
                          // 处理剩余部分
                          const remaining = cleanTag.replace(/科普|军事/g, '').replace(/[,，\s]+/g, ' ').trim();
                          if (remaining) {
                            // 按空格或逗号分割剩余部分
                            const parts = remaining.split(/[,，\s]+/).filter(part => part.trim());
                            processedTags.push(...parts);
                          }
                        } else if (cleanTag.includes('科普')) {
                          // 如果只包含科普
                          processedTags.push('科普');
                          // 处理剩余部分
                          const remaining = cleanTag.replace('科普', '').replace(/^[,，\s]+|[,，\s]+$/g, '').trim();
                          if (remaining) {
                            const parts = remaining.split(/[,，\s]+/).filter(part => part.trim());
                            processedTags.push(...parts);
                          }
                        } else if (cleanTag.includes('军事')) {
                          // 如果只包含军事
                          processedTags.push('军事');
                          // 处理剩余部分
                          const remaining = cleanTag.replace('军事', '').replace(/^[,，\s]+|[,，\s]+$/g, '').trim();
                          if (remaining) {
                            const parts = remaining.split(/[,，\s]+/).filter(part => part.trim());
                            processedTags.push(...parts);
                          }
                        } else {
                          // 普通标签，可能包含逗号分隔的多个标签
                          const parts = cleanTag.split(/[,，]+/).map(part => part.trim()).filter(part => part);
                          processedTags.push(...parts);
                        }
                      });
                      
                      // 去重
                      const uniqueTags = [...new Set(processedTags)];
                      
                      // 分离特殊标签和普通标签
                      const specialTags = uniqueTags.filter(tag => tag === '科普' || tag === '军事');
                      const normalTags = uniqueTags.filter(tag => tag !== '科普' && tag !== '军事');
                      
                      console.log('[NewsCard] Processed tags:', { original: displayTags, processed: uniqueTags, special: specialTags, normal: normalTags });
                      
                      return (
                        <>
                          {/* 先显示普通标签 */}
                          {normalTags.map((tag, index) => (
                            <span
                              key={`normal-${tag}-${index}`}
                              className="
                                px-2 py-1 text-xs rounded-full
                                bg-blue-500/10 text-blue-400
                              "
                            >
                              {tag}
                            </span>
                          ))}
                          {/* 后显示特殊标签 */}
                          {specialTags.map((tag, index) => (
                            <span
                              key={`special-${tag}-${index}`}
                              className={`
                                px-2 py-1 text-xs rounded-full font-medium
                                ${tag === '科普' 
                                  ? 'bg-green-500/20 text-green-400 border border-green-500/30' 
                                  : 'bg-red-500/20 text-red-400 border border-red-500/30'
                                }
                              `}
                            >
                              {tag}
                            </span>
                          ))}
                        </>
                      );
                    })()}
                  </div>
                </div>
              )}

              {/* 底部信息栏 - 响应式布局 */}
              <div className="flex items-center justify-start gap-2 pt-2 border-t border-white/5">
                <div className="flex items-center gap-2 text-sm text-gray-400">
                  <Calendar className="w-4 h-4" />
                  <span>{date}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </a>
    </motion.article>
  );
}