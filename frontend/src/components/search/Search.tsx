import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate, useLocation } from 'react-router-dom';
import { SearchResults } from './results/SearchResults';
import { motion } from 'framer-motion';
import { Header } from '../layout/Header';
import { Background } from '../Background';
import { SearchTypeToggle } from './SearchTypeToggle';
import { IntelligentSearch } from './IntelligentSearch';
import { RegularSearch } from './regular/RegularSearch';
import { MainContentWrapper } from '../layout/MainContentWrapper';
import { BackToTop } from '../common/BackToTop';

export default function Search() {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchText, setSearchText] = useState(searchParams.get('q') || '');
  const [isSearching, setIsSearching] = useState(!!searchParams.get('q'));
  const [searchType, setSearchType] = useState<'intelligent' | 'regular'>('intelligent');
  const [regularSearchActive, setRegularSearchActive] = useState(false);

  const handleSearch = async (query: string) => {
    if (query.trim()) {
      setSearchParams({ q: query.trim() });
      setIsSearching(true);
    }
  };

  useEffect(() => {
    const query = searchParams.get('q');
    if (query) {
      setSearchText(query);
      setIsSearching(true);
    }
  }, [searchParams]);

  // 调试信息
  useEffect(() => {
    const backToTopEnabled =
      (searchType === 'intelligent' && isSearching && !!searchText) ||
      (searchType === 'regular' && regularSearchActive);

    console.log('BackToTop Debug:', {
      searchType,
      isSearching,
      searchText: !!searchText,
      regularSearchActive,
      enabled: backToTopEnabled
    });
  }, [searchType, isSearching, searchText, regularSearchActive]);

  return (
    <div className="min-h-screen">
      <Background />
      <Header />
      <MainContentWrapper>
        <motion.div 
          className="w-full flex flex-col items-center"
          style={{
            marginTop: isSearching ? '20px' : '120px'
          }}
          animate={{
            marginTop: isSearching ? '20px' : '120px'
          }}
          transition={{ duration: 0.3 }}
        >
          {/* 搜索类型切换 */}
          <SearchTypeToggle 
            activeType={searchType}
            onToggle={setSearchType}
          />

          {/* 搜索框 */}
          <div className="mt-6 w-full max-w-4xl">
            {searchType === 'intelligent' ? (
              <IntelligentSearch 
                onSearch={handleSearch}
                disabled={false}
                initialValue={searchText}
                showRecommendations={false}
              />
            ) : (
              <RegularSearch
                onSearch={() => handleSearch('高级搜索')}
                disabled={false}
                onSearchStateChange={setRegularSearchActive}
              />
            )}
          </div>

          {/* 搜索结果 - 只在智能搜索模式下显示 */}
          {isSearching && searchText && searchType === 'intelligent' && (
            <SearchResults searchText={searchText} />
          )}
        </motion.div>
      </MainContentWrapper>

      {/* 测试按钮 - 用于调试 */}
      <div
        style={{
          position: 'fixed',
          bottom: '100px',
          right: '32px',
          zIndex: 99999,
          width: '48px',
          height: '48px',
          backgroundColor: 'red',
          color: 'white',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer'
        }}
        onClick={() => {
          const scrollContainer = document.querySelector('[data-scroll-container="main"]');
          if (scrollContainer) {
            scrollContainer.scrollTo({ top: 0, behavior: 'smooth' });
          }
        }}
      >
        TEST
      </div>

      {/* Back to Top Button - 在搜索结果显示时启用 */}
      <BackToTop
        threshold={100}
        forceWindowScroll={false}
        scrollContainer='[data-scroll-container="main"]'
        enabled={true}
      />
    </div>
  );
} 