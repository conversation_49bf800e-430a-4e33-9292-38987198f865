import React, { useRef, useState } from 'react';
import { SearchHeader } from './SearchHeader';
import { SpaceTargetSearch } from './SpaceTargetSearch';
import { HomeRecommendations } from '../HomeRecommendations';

interface RegularSearchProps {
  onSearch: () => void;
  disabled?: boolean;
  onSearchStateChange?: (isSearching: boolean) => void;
}

export function RegularSearch({ onSearch, disabled, onSearchStateChange }: RegularSearchProps) {
  console.log('RegularSearch rendered');
  
  // 创建对 SpaceTargetSearch 组件的引用
  const spaceTargetSearchRef = useRef<{ handleSearch: () => void }>(null);
  const [isSearching, setIsSearching] = useState(false);

  // 当父组件触发搜索时，调用SpaceTargetSearch的handleSearch
  React.useEffect(() => {
    if (onSearch && spaceTargetSearchRef.current) {
      // 利用SpaceTargetSearch组件自身的搜索按钮进行搜索，不需要额外的按钮
      const originalOnSearch = onSearch;
      onSearch = () => {
        if (spaceTargetSearchRef.current) {
          spaceTargetSearchRef.current.handleSearch();
        }
        originalOnSearch();
      };
    }
  }, [onSearch]);

  return (
    <div 
      className="
        w-full max-w-4xl mx-auto 
        rounded-xl p-6
      "
      style={{ minHeight: '400px' }}
    >
      <SearchHeader />
      <SpaceTargetSearch
        ref={spaceTargetSearchRef}
        onSearch={() => {
          setIsSearching(true);
          onSearchStateChange?.(true);
        }}
      />
      
      {/* 智能推荐 - 只在未搜索时显示 */}
      {!isSearching && (
        <div className="mt-8">
          <div className="w-[80%] mx-auto">
            <HomeRecommendations />
          </div>
        </div>
      )}
    </div>
  );
}