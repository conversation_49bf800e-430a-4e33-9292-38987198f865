import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronUp } from 'lucide-react';

interface BackToTopProps {
  /**
   * 显示按钮的滚动阈值（像素）
   * @default 100
   */
  threshold?: number;
  /**
   * 按钮的位置
   * @default 'bottom-right'
   */
  position?: 'bottom-right' | 'bottom-left';
  /**
   * 自定义样式类名
   */
  className?: string;
  /**
   * 是否启用
   * @default true
   */
  enabled?: boolean;
  /**
   * 自定义滚动容器选择器
   */
  scrollContainer?: string;
  /**
   * 强制使用window滚动
   * @default false
   */
  forceWindowScroll?: boolean;
}

/**
 * 回到顶部按钮组件
 * 当页面滚动超过指定阈值时显示，点击后平滑滚动到页面顶部
 * 自动检测滚动容器，支持MainContentWrapper等自定义滚动容器
 */
export function BackToTop({ 
  threshold = 100, 
  position = 'bottom-right',
  className = '',
  enabled = true,
  scrollContainer,
  forceWindowScroll = false
}: BackToTopProps) {
  const [isVisible, setIsVisible] = useState(false);
  const scrollElementRef = useRef<Element | Window | null>(null);

  // 查找滚动容器
  useEffect(() => {
    if (!enabled) return;

    let scrollElement: Element | Window = window;

    if (forceWindowScroll) {
      // 强制使用window滚动
      scrollElement = window;
    } else if (scrollContainer) {
      // 如果指定了滚动容器选择器
      const element = document.querySelector(scrollContainer);
      if (element) {
        scrollElement = element;
      }
    } else {
      // 自动检测滚动容器
      // 优先查找带有data-scroll-container属性的元素
      const mainScrollContainer = document.querySelector('[data-scroll-container="main"]');
      if (mainScrollContainer) {
        scrollElement = mainScrollContainer;
      } else {
        // 备用方案：查找MainContentWrapper的滚动容器（通过标签名和位置特征）
        const mainElements = document.querySelectorAll('main');
        for (const main of mainElements) {
          const computedStyle = window.getComputedStyle(main);
          if (computedStyle.position === 'fixed' && 
              computedStyle.overflowY === 'auto') {
            scrollElement = main;
            break;
          }
        }
      }
    }

    scrollElementRef.current = scrollElement;

    // 调试信息：显示找到的滚动容器
    console.log('BackToTop Container Debug:', {
      enabled,
      forceWindowScroll,
      scrollContainer,
      foundElement: scrollElement === window ? 'window' : (scrollElement as Element).tagName,
      elementSelector: scrollElement === window ? 'window' : (scrollElement as Element).getAttribute('data-scroll-container')
    });

    const handleScroll = () => {
      let scrollTop = 0;

      if (scrollElement === window) {
        scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      } else {
        scrollTop = (scrollElement as Element).scrollTop;
      }

      const shouldShow = scrollTop > threshold;

      // 调试信息
      console.log('BackToTop Scroll Debug:', {
        scrollElement: scrollElement === window ? 'window' : 'element',
        scrollTop,
        threshold,
        shouldShow,
        currentVisible: isVisible
      });

      setIsVisible(shouldShow);
    };

    // 添加滚动监听器
    scrollElement.addEventListener('scroll', handleScroll, { passive: true });
    
    // 初始检查
    handleScroll();

    // 清理函数
    return () => {
      scrollElement.removeEventListener('scroll', handleScroll);
    };
  }, [threshold, enabled, scrollContainer, forceWindowScroll]);

  // 回到顶部函数
  const scrollToTop = () => {
    const scrollElement = scrollElementRef.current;
    
    if (!scrollElement) return;

    if (scrollElement === window) {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    } else {
      (scrollElement as Element).scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }
  };

  // 根据位置设置样式
  const positionStyles = {
    'bottom-right': { bottom: '32px', right: '32px' },
    'bottom-left': { bottom: '32px', left: '32px' }
  };

  if (!enabled) return null;

  const buttonStyle: React.CSSProperties = {
    position: 'fixed',
    zIndex: 99999, // 增加z-index确保在最顶层
    width: '48px',
    height: '48px',
    backgroundColor: 'rgba(59, 130, 246, 0.9)', // 增加不透明度
    color: 'white',
    border: '2px solid rgba(255, 255, 255, 0.3)', // 添加边框增加可见性
    borderRadius: '50%',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    boxShadow: '0 8px 24px rgba(0, 0, 0, 0.5)', // 增强阴影
    backdropFilter: 'blur(8px)',
    transition: 'all 0.2s ease',
    ...positionStyles[position]
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.button
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          whileHover={{ scale: 1.1, backgroundColor: 'rgba(59, 130, 246, 0.9)' }}
          whileTap={{ scale: 0.9 }}
          onClick={scrollToTop}
          style={buttonStyle}
          className={className}
          aria-label="回到顶部"
          title="回到顶部"
        >
          <ChevronUp size={24} />
        </motion.button>
      )}
    </AnimatePresence>
  );
} 