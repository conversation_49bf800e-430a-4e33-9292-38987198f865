import React, { useState, useRef, useEffect } from 'react';
import { User, UserCircle, Tag, Crown, LogOut, Shield } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuthStore } from '../../store/auth';
import { UserRole } from '../../types/auth';

/**
 * 用户菜单组件
 * 显示用户头像和下拉菜单，包含个人信息、实体关注管理、关键词订阅和会员权限管理等选项
 * 根据登录状态显示不同的菜单项
 */
export function UserMenu() {
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();
  
  // 获取认证状态
  const { isAuthenticated, userInfo, logout, isAdmin } = useAuthStore();

  // 点击外部关闭菜单
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 已登录用户的菜单项
  const authenticatedMenuItems = [
    {
      title: '个人信息',
      icon: UserCircle,
      path: '/settings/profile'
    },
    {
      title: '关键词订阅',
      icon: Tag,
      path: '/settings/keywords',
      requiredRole: UserRole.ADMIN
    },
    {
      title: '会员权限管理',
      icon: Crown,
      path: '/settings/membership',
      requiredRole: UserRole.ADMIN
    }
  ];

  // 根据用户权限过滤菜单项
  const filteredAuthenticatedMenuItems = authenticatedMenuItems.filter(item => {
    // 如果菜单项没有权限要求，则所有用户都可以看到
    if (!item.requiredRole) {
      return true;
    }
    
    // 检查用户是否具有所需角色
    return userInfo?.role === item.requiredRole;
  });

  // 管理员专用菜单项
  const adminMenuItem = {
    title: '管理员面板',
    icon: Shield,
    path: '/admin'
  };

  // 处理退出登录
  const handleLogout = () => {
    logout();
    setIsOpen(false);
    navigate('/');
  };

  // 获取用户角色对应的文本
  const getRoleText = (role?: UserRole) => {
    switch (role) {
      case UserRole.ADMIN:
        return '管理员';
      case UserRole.FREE:
      default:
        return '普通用户';
    }
  };

  return (
    <div className="relative" ref={menuRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-10 h-10 inline-flex items-center justify-center rounded-lg text-white/70 hover:text-white hover:bg-white/5 transition-all duration-200"
        aria-label="用户菜单"
      >
        {isAuthenticated && userInfo?.avatar ? (
          <img 
            src={userInfo.avatar} 
            alt={userInfo.username} 
            className="w-7 h-7 rounded-full object-cover"
          />
        ) : (
          <User className="w-5 h-5" />
        )}
      </button>

      {isOpen && (
        <div
          className="
            absolute right-0 mt-2 w-56
            rounded-lg
            backdrop-blur-md
            bg-white/20
            border border-white/20
            shadow-lg py-2
            z-50
          "
          style={{
            backdropFilter: 'blur(12px)'
          }}
        >
          {isAuthenticated ? (
            // 已登录状态显示用户菜单
            <>
              {/* 用户信息头部 */}
              {userInfo && (
                <div className="px-4 py-3 border-b border-white/10 mb-2">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      {userInfo.avatar ? (
                        <img 
                          src={userInfo.avatar} 
                          alt={userInfo.username} 
                          className="w-10 h-10 rounded-full object-cover"
                        />
                      ) : (
                        <div className="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center">
                          <span className="text-white font-medium text-sm">
                            {userInfo.username.substring(0, 2).toUpperCase()}
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-white">{userInfo.username}</p>
                      <p className="text-xs text-gray-300">{getRoleText(userInfo.role)}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* 管理员专用菜单项 */}
              {isAdmin() && (
                <>
                  <Link
                    to={adminMenuItem.path}
                    className="flex items-center gap-3 px-4 py-2 text-sm text-white/70 hover:text-white hover:bg-white/5 transition-colors"
                    onClick={() => setIsOpen(false)}
                  >
                    <adminMenuItem.icon className="w-4 h-4" />
                    <span>{adminMenuItem.title}</span>
                  </Link>
                  <div className="border-b border-white/10 my-2"></div>
                </>
              )}

              {/* 普通菜单项 */}
              {filteredAuthenticatedMenuItems.map((item, index) => {
                const Icon = item.icon;
                return (
                  <Link
                    key={index}
                    to={item.path}
                    className="flex items-center gap-3 px-4 py-2 text-sm text-white/70 hover:text-white hover:bg-white/5 transition-colors"
                    onClick={() => setIsOpen(false)}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{item.title}</span>
                  </Link>
                );
              })}
              
              {/* 退出登录选项 */}
              <div className="border-t border-white/10 mt-2 pt-2">
                <button
                  className="w-full flex items-center gap-3 px-4 py-2 text-sm text-white/70 hover:text-white hover:bg-white/5 transition-colors"
                  onClick={handleLogout}
                >
                  <LogOut className="w-4 h-4" />
                  <span>退出登录</span>
                </button>
              </div>
            </>
          ) : (
            // 未登录状态显示登录选项
            <Link
              to="/login"
              className="flex items-center gap-3 px-4 py-2 text-sm text-white/70 hover:text-white hover:bg-white/5 transition-colors"
              onClick={() => setIsOpen(false)}
            >
              <UserCircle className="w-4 h-4" />
              <span>登录</span>
            </Link>
          )}
        </div>
      )}
    </div>
  );
} 