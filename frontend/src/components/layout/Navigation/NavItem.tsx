import React from 'react';
import { LucideIcon } from 'lucide-react';

interface NavItemProps {
  icon: LucideIcon;
  label: string;
  href: string;
  isMobile?: boolean;
}

export function NavItem({ icon: Icon, label, href, isMobile }: NavItemProps) {
  const isActive = href === window.location.pathname;
  
  if (isMobile) {
    return (
      <a
        href={href}
        className={`
          flex items-center gap-3 w-full
          px-4 py-2
          text-sm font-medium
          transition-all duration-200
          ${isActive
            ? 'text-white bg-white/10'
            : 'text-white/70 hover:text-white hover:bg-white/5'
          }
        `}
      >
        <Icon className="w-4 h-4" />
        <span className="flex-1">{label}</span>
        {isActive && (
          <div className="w-2 h-2 rounded-full bg-blue-400" />
        )}
      </a>
    );
  }
  
  // 桌面端样式保持不变
  return (
    <a
      href={href}
      className={`
        group flex items-center h-10
        rounded-lg
        transition-all duration-200
        ${isActive ? 'text-blue-400' : 'text-white/70 hover:text-white'}
        px-3
      `}
    >
      <Icon className="w-5 h-5" />
      <span className="ml-2 font-medium whitespace-nowrap text-sm">
        {label}
      </span>
    </a>
  );
}