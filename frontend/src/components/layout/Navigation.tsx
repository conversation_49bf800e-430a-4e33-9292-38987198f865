import React, { useState } from 'react';
import { Menu, LucideIcon } from 'lucide-react';
import { useMediaQuery } from '../../hooks/useMediaQuery';
import { navItems } from '../../data/navigation';

function NavItem({ icon: Icon, label, href, isMobile }: {
  icon: LucideIcon;
  label: string;
  href: string;
  isMobile?: boolean;
}) {
  const isActive = href === window.location.pathname;

  if (isMobile) {
    return (
      <a
        href={href}
        className={`
          flex items-center gap-3 w-full
          px-4 py-2
          text-sm font-medium
          transition-all duration-200
          ${isActive
            ? 'text-white bg-white/10'
            : 'text-white/70 hover:text-white hover:bg-white/5'
          }
        `}
      >
        <Icon className="w-4 h-4" />
        <span className="flex-1">{label}</span>
        {isActive && (
          <div className="w-2 h-2 rounded-full bg-blue-400" />
        )}
      </a>
    );
  }

  // 桌面端样式保持不变
  return (
    <a
      href={href}
      className={`
        group flex items-center h-10
        rounded-lg
        transition-all duration-200
        ${isActive ? 'text-blue-400' : 'text-white/70 hover:text-white'}
        px-3
      `}
    >
      <Icon className="w-5 h-5" />
      <span className="ml-2 font-medium whitespace-nowrap text-sm">
        {label}
      </span>
    </a>
  );
}

function NavList({ isMobile = false }) {
  // 需要在其后添加分割线的导航项（根据标签名判断）
  const shouldAddDividerAfter = (label: string) => {
    return ['首页', '搜索', '新闻', '发射'].includes(label);
  };

  return (
    <div className={`
      ${isMobile ? 'flex-col w-full space-y-1' : 'flex items-center h-14 space-x-1'}
    `}>
      {navItems.map((item, index) => (
        <React.Fragment key={item.label}>
          <NavItem
            {...item}
            isMobile={isMobile}
          />
          {/* 移动端添加分割线 */}
          {isMobile && shouldAddDividerAfter(item.label) && index < navItems.length - 1 && (
            <div className="mx-4 my-2">
              <div className="h-px bg-gradient-to-r from-transparent via-white/20 to-transparent" />
            </div>
          )}
        </React.Fragment>
      ))}
    </div>
  );
}

export function Navigation() {
  const [isOpen, setIsOpen] = useState(false);
  const isMobile = useMediaQuery('(max-width: 768px)');

  return (
    <nav className="flex-1 flex items-center justify-end h-14">
      {isMobile ? (
        <>
          <button
            onClick={() => setIsOpen(!isOpen)}
            className="w-10 h-10 inline-flex items-center justify-center rounded-lg text-white/70 hover:text-white hover:bg-white/5 transition-all duration-200"
            aria-label="主菜单"
            aria-expanded={isOpen}
          >
            <Menu className="h-5 w-5" aria-hidden="true" />
          </button>

          <div
            className={`
              fixed inset-0 z-40
              transition-all duration-300 ease-in-out
              ${isOpen ? 'visible opacity-100' : 'invisible opacity-0 pointer-events-none'}
            `}
          >
            {/* Backdrop */}
            <div
              className="absolute inset-0 bg-black/20 backdrop-blur-sm"
              onClick={() => setIsOpen(false)}
            />

            {/* Menu Panel - 改为弹出层样式，从右侧显示 */}
            <div
              className={`
                absolute right-4 top-16
                w-64 max-h-[calc(100vh-5rem)]
                rounded-lg
                backdrop-blur-md
                bg-white/20
                border border-white/20
                shadow-lg
                transition-all duration-300 ease-out
                ${isOpen ? 'opacity-100 scale-100 translate-y-0' : 'opacity-0 scale-95 -translate-y-2'}
                overflow-hidden
              `}
              style={{
                backdropFilter: 'blur(12px)'
              }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* 菜单头部 */}
              <div className="px-4 py-3 bg-gradient-to-r from-blue-600/20 to-purple-600/20 border-b border-white/20">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center shadow-lg">
                      <svg className="w-4 h-4 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-semibold text-white">快速导航</h3>
                    <p className="text-xs text-white/70">太空数据平台</p>
                  </div>
                </div>
              </div>

              {/* 菜单内容 */}
              <div className="py-2 max-h-96 overflow-y-auto">
                <NavList isMobile />
              </div>
            </div>
          </div>
        </>
      ) : (
        <NavList />
      )}
    </nav>
  );
}