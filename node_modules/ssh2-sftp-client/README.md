- [Overview](#org4015600)
  - [Version 12.0.0 Changes](#org9ba2cfa)
  - [Version 12.0.1 Changes](#org658a511)
  - [Background](#org4d07682)
- [Installation](#orgb1c417b)
- [Basic Usage](#orgbddd3fd)
- [Documentation](#org8c374be)
  - [Specifying Paths](#org95c6689)
  - [Methods](#org8437ec6)
    - [new SftpClient(name, callbacks) ===> SFTP client object](#org210a6e7)
    - [connect(config) ===> SFTP object](#org9c48be5)
    - [list(path, filter) ==> Array[object]](#org190df3b)
    - [exists(path) ==> boolean](#org7d245b2)
    - [stat(path) ==> object](#orga6a58bd)
    - [get(path, dst, options) ==> String|Stream|Buffer](#orgc3ea293)
    - [fastGet(remotePath, localPath, options) ===> string](#org6261e44)
    - [put(src, remotePath, options) ==> string](#org6c23ce2)
    - [fastPut(localPath, remotePath, options) ==> string](#orgfda72d4)
    - [append(input, remotePath, options) ==> string](#org6c67ab9)
    - [mkdir(path, recursive) ==> string](#org99a7d1c)
    - [rmdir(path, recursive) ==> string](#org31ab7d2)
    - [delete(path, noErrorOK) ==> string](#org3a4a52a)
    - [rename(fromPath, toPath) ==> string](#org9fbf1b2)
    - [posixRename(fromPath, toPath) ==> string](#org0be5342)
    - [chmod(path, mode) ==> string](#org9227f61)
    - [realPath(path) ===> string](#orgdbe4247)
    - [cwd() ==> string](#orge856d52)
    - [uploadDir(srcDir, dstDir, options) ==> string](#org9337523)
    - [downloadDir(srcDir, dstDir, options) ==> string](#org30526ff)
    - [createReadStream(remotePath, options)) ==> stream object](#org98dacc6)
    - [createWriteStream(remotePath, options) ==> stream object](#orgf250341)
    - [rcopy(srcPath, dstPath) ==> string](#org16daa1a)
    - [end() ==> boolean](#orgb329f2e)
    - [Add and Remove Listeners](#org80dffcb)
- [Platform Quirks & Warnings](#org6e83e30)
  - [Server Capabilities](#org75bad4f)
  - [Issues with `fastPut()` and `fastGet()` Methods](#org3fe5a31)
  - [Promises, Events & Managing Exceptions](#org9fd1871)
    - [Adding Custom Handlers](#org256ad96)
  - [Windows Based Servers](#org1eef7b1)
  - [Don't Re-use SftpClient Objects](#org37ac59e)
- [FAQ](#orgc01e9f3)
  - [Remote server drops connections with only an end event](#orgcbb84bd)
  - [How can I pass writeable stream as dst for get method?](#orgb7ba758)
  - [How can I upload files without having to specify a password?](#orgf9d915c)
  - [How can I connect through a Socks Proxy](#orgc977636)
  - [Timeout while waiting for handshake or handshake errors](#org9caf1fc)
  - [How can I limit upload/download speed](#orgc99cfed)
  - [Connection hangs or fails for larger files](#orgff1cf60)
  - [Typescript definition file out of date](#org312104a)
- [Examples](#org409c3ef)
- [Troubleshooting](#orge118375)
  - [Common Errors](#org39ed676)
    - [Not returning the promise in a `then()` block](#orge4fdace)
    - [Mixing Promise Chains and Async/Await](#org613b5f0)
    - [Try/catch and Error Handlers](#org3df8f9d)
    - [Server Differences](#org295cfa3)
    - [Avoid Concurrent Operations](#org5ee2ce1)
  - [Debugging Support](#orgec0b62a)
- [Logging Issues](#org41f81c7)
- [Pull Requests](#orgd153e6d)
- [Contributors](#org267dc2a)



<a id="org4015600"></a>

# Overview

This package provides the class SftpClient, an SFTP client for node.js. It is a promise based decorator class around the excellent [SSH2](https://github.com/mscdex/ssh2) package, which provides a pure node Javascript event based ssh2 implementation.

Documentation on the methods and available options in the underlying modules can be found on the [SSH2](https://github.com/mscdex/ssh2) project pages. As the ssh2-sftp-client package is just a wrapper around the `ssh2` module, you will find lots of useful information, tips and examples in the `ssh2` repository.

Current stable release is \*v12.0.1.

Code has been tested against Node versions 20.19.2, 22.16.0, 23.11.1 and 24.2.0. Node versions prior to v20.x are not supported.

If you find this module useful and you would like to support the on-going maintenance and support of users, please consider making a small [donation](https://square.link/u/gB2kSdkY?src=embed).


<a id="org9ba2cfa"></a>

## Version 12.0.0 Changes

The big and breaking change in this version is the removal of the connection retry code. This module no longer supports automatic connection retries when establishing the initial connection. This functionality was removed for a number of reasons. The main reasons are that it significantly complicated the connection handling code and error handling in particular and it was seldom useful. In most cases, if the connection failed on the first attempt, it would also fail in all subsequent attempts and often resulted in just extending the time to failure as multiple retries were attempted.

Another reason this functionality was removed is because if you really want it, it is straight forward to create your own wrapper around the connect call which uses one of the mnay promise retry packages available. It is much simpler to add a retry mechanism for a specific application than it is to implement a flexible solution which supports all possible combination of connection options, authentication methods, proxies etc.


<a id="org658a511"></a>

## Version 12.0.1 Changes

Bug Fixes

-   Fix typo error in specification of generic error code in error handler. Typo was causing an undefined symbol error.
-   Fix bug in stream error handling. Read and write streams had an error handler defined with 'once' rather than 'on'. This caused a bug when an API call caused more than one error. For example, a network connectivity error would cause a stream timeout error followed by a no response from remote host error when the error handler attempted to cleanup and destroy the remote stream. The second error whould not be handled and would bubble up to the parent node process, which would then exit with an unhandled exception error.
-   Added calls to \_resetEventFlags() to finally clauses to ensure event flags are reset when temp listeners are removed. Without this change, global listeners would not fire when they should after their has been an earlier handled error in the last API call.


<a id="org4d07682"></a>

## Background

In basic terms `ssh2-sftp-client` is a simple wrapper around the `ssh2` package which provides a promise based API for interacting with a remote SFTP server . The `ssh2` package provides an event based API for interacting with the `ssh` protocol. The `ssh2-sftp-client` package uses the `sftp` subsystem of this protocol to implement the basic operations typically associated with an `sftp` client.

Wrapping an event based API with a promise based API comes with a number of challenges. In particular, efficiently and reliably managing events within the context of asynchronous code execution. This package uses the following strategies;

-   All direct interactions with the `ssh2` API are wrapped in promise objects. When the API call succeeds, the associated promise will be successfully resolved. When an error occurs, the promise is rejected.

-   An error can either be due to a low level network error, such as a lost connection to the remote server or due to an operational error, such as a file not existing or not having the appropriate permissions for access.

-   Each of the available `SftpClient` methods wrap the method call inside a promise. In creating each promise, the class adds temporary event listeners for the error, end and close events and links those liseners to the method's promise via its `reject()` method.

-   If a promise is waiting to be fulfilled when an error occurs, the error will be communicated back to the client code via the rejected promise.

-   When the `ssh2` emitter raises an event outside the context of any promise, that event will be handled by global event handlers. By default, these event handlers will log the event and will invalidate any existing socket connection objects, preventing any further API calls until a new connection is established.

-   The `SftpClient` class constructor supports an optoinal second argument which is an objedct which can have any of the three properties error, end and close. The values associated with these properties are callback functions that will be run if an error event with?he same name as the property is raised and has not been handled by one of the temporary event handlers. The error callback should have a single parameter, the error raised by the event. The other callbacks have no arguments.

The need for both global listeners and temporary promise listeners is because network end, close or error events can occur at any time, including in-between API calls. During an API call, a promise is active and can be used to communicate event information back to the calling code via normal promise communication channels i.e. async/await with try/catch or promise chain's then/catch mechanism. However, outside API calls, no promise exists and there is no reliable mechanism to return error and other event information back to calling code. You cannot reliably use try/cdatch to catch errors thrown inside event listenrs as you lack control over when the listener code runs. Your try/catch block can easily complete before the error is raised as there is no equivalent *await* type functionality in this situation.

As there is no simple default way to return error and other event information back to the calling code, `ssh2-sftp-client` doesn't try to. Instead, the default action is to just log the event information and invalidate any existing sftp connections. This strategy is often sufficient for many use cases. For those cases where it isn't, client code can pass in end, close and/or error listener functions when instantiating the `SftpClient` object. If provided, these listners will be executed whenever the default global listeners are executed, which is whenever the `ssh2` event emitter raises an end, close or error event which is not handled by one of the temporary promise linked event listeners.

Version 11 of `ssh2-sftp-client` also changes the behaviour of the temporary promise linked *end* and *close* listeners. Prior to version 11, these listeners did not reject promises. They would only invalidate the underlying `ssh` connection object. Only the *error* listener would actually reject the associated promise. This was done because you cannot guarantee the order in which events are responded to.

In most cases, events will occur in the order *error*, *end* and then *close*. The error event would contain details about the cause of the error while the end and close events just communicate that these events have been raised. The normal flow would be

-   Error event occurs including error cause description. Listener catches event, creates an error object and calls the associated promise's reject function to reject the promise. The calling process receives a rejected promise object.

-   End event occurs. The end listener catches the event and marks the connection object as invalid as the socket connection has been ended. There is no need to call the reject method of the associated promise as it has already been called by the error listener and you can only call one promise resolution function.

-   Close event occurs. This event means the socket connection has been closed. The listener will ensure any connection information has been invalidated. Again, there is no need to call the reject method of the associated promise as it has already been called by the error listener.

In some cases, no end event is raised and you only get an error event followed by a close event. In versions of `ssh2-sftp-client` prior to version 11, neither the end or the close listeners attempted to call the reject method of the associated promise. It was assumed that all sftp servers would raise an error event whenever a connection was unexpectedly ended or closed. Unfortunately, it turns out some sftp servers are not well behaved and will terminate the connection without providing any error information or raising an error event. When this occurred in versions prior to version 11, it could result in either an API call hanging because its associated promise never gets rejected or resolved or the call gets rejected with a timeout error aftrer a significant delay.

In order to handle the possible hanging issue in version 11, the temporary promise linked end and close listeners have been updated to always call the promise's reject function if they fire. While this works, it can cause a minor issue. As wse cannot gurantee the order in which events are responded to by listeners, it is possible that either the end or close listener may be executed before the error listener. When this occurs, the promise is rejected, but the only information wse have at that point is that the promise wsas reject due to either an end or close event. We don't yet have any details regarding what error has caused the unexpected end or close event. Furthermore, because only the first promise resolution function call has any effect, calling reject within the error listener (assuming an error event does eventually arrive) has no effect and does not communicate error information back to the caller. This means that in some circumstances, especially when working with some poorly behaved sftp servers, an sftp connection will be lost/closed with no indication as to reason. This can make diagnosis and bug tracking frustrating.


<a id="orgb1c417b"></a>

# Installation

```shell
npm install ssh2-sftp-client
```


<a id="orgbddd3fd"></a>

# Basic Usage

```js
let Client = require('ssh2-sftp-client');
let sftp = new Client();

sftp.connect({
  host: '127.0.0.1',
  port: '8080',
  username: 'username',
  password: '******'
}).then(() => {
  return sftp.list('/pathname');
}).then(data => {
  console.log(data, 'the data info');
}).catch(err => {
  console.log(err, 'catch error');
});
```


<a id="org8c374be"></a>

# Documentation

The connection options are the same as those offered by the underlying SSH2 module, with just a couple of additional properties added to add a `debug` function and set the `promiseLimit` property. For full details on the other properties, please see [SSH2 client methods](https://github.com/mscdex/ssh2#user-content-client-methods). In particular, see the `ssh2` documentation for details relating to setting various key exchange and encryption/signing algorithms used as part of the ssh2 protocol.

All the methods will return a Promise, except for `on(), ~removeListener()`, `createReadStream` and `createWriteStream`, which are typically only used in special use cases.

Note that I don't use Typescript and I don't maintain any typescript definition files. There are some typescript type definition files for this module, but they are maintained separately and have nothing to do with this project. Therefore, please do not log any issues arising from the use of these definition files with this project. Instead, refer your issues to the maintainers of those modules.


<a id="org95c6689"></a>

## Specifying Paths

The convention with both FTP and SFTP is that paths are specified using a 'nix' style i.e. use `/` as the path separator. This means that even if your SFTP server is running on a win32 platform, you should use `/` instead of `\` as the path separator. For example, for a win32 path of `C:\Users\<USER>\_get Permission Denied error in ssh2-streams.

```javascript


const Throttle = require('throttle');
const progress = require('progress-stream');

// limit download speed
const throttleStream = new Throttle(config.throttle);

// download progress stream
const progressStream = progress({
  length: fileSize,
  time: 500,
});
progressStream.on('progress', (progress) => {
  console.log(progress.percentage.toFixed(2));
});

const outStream = createWriteStream(localPath);

// pipe streams together
throttleStream.pipe(progressStream).pipe(outStream);

try {
  // set autoClose to false
  await client.get(remotePath, throttleStream, { autoClose: false });
} catch (e) {
  console.log('sftp error', e);
} finally {
  await client.end();
}
```


<a id="orgff1cf60"></a>

## Connection hangs or fails for larger files

This was contributed by Ladislav Jacho. Thanks.

A symptom of this issue is that you are able to upload small files, but uploading larger ones fail. You probably have an MTU/fragmentation problem. For each network interface on both client and server set the MTU to 576, e.g. `ifconfig eth0 mtu 576`. If that works, you need to find the largest MTU which will work for your network. An MTU which is too small will adversely affect throughput speed. A common value to use is an MTU of 1400.

For more explanation, see [issue #342](https://github.com/theophilusx/ssh2-sftp-client/issues/342).


<a id="org312104a"></a>

## Typescript definition file out of date

This project does not use Typescript. However, typescript definition files are provided by other 3rd parties. Sometimes, these definition files have not stayed up-to-date with the current version of this module. If you encounter this issue, you need to report it to the party responsible for the definition file, not this project.


<a id="org409c3ef"></a>

# Examples

I have started collecting example scripts in the example directory of the repository. These are mainly scripts I have put together in order to investigate issues or provide samples for users. They are not robust, lack adequate error handling and may contain errors. However, I think they are still useful for helping developers see how the module and API can be used.


<a id="orge118375"></a>

# Troubleshooting

The `ssh2-sftp-client` module is essentially a wrapper around the `ssh2` and `ssh2-streams` modules, providing a higher level `promise` based API. When you run into issues, it is important to try and determine where the issue lies - either in the ssh2-sftp-client module or the underlying `ssh2` and `ssh2-streams` modules. One way to do this is to first identify a minimal reproducible example which reproduces the issue. Once you have that, try to replicate the functionality just using the `ssh2` and `ssh2-streams` modules. If the issue still occurs, then you can be fairly confident it is something related to those later 2 modules and therefore and issue which should be referred to the maintainer of that module.

The `ssh2` and `ssh2-streams` modules are very solid, high quality modules with a large user base. Most of the time, issues with those modules are due to client misconfiguration. It is therefore very important when trying to diagnose an issue to also check the documentation for both `ssh2` and `ssh2-streams`. While these modules have good defaults, the flexibility of the ssh2 protocol means that not all options are available by default. You may need to tweak the connection options, ssh2 algorithms and ciphers etc for some remote servers. The documentation for both the `ssh2` and `ssh2-streams` module is quite comprehensive and there is lots of valuable information in the issue logs.

If you run into an issue which is not repeatable with just the `ssh2` and `ssh2-streams` modules, then please log an issue against the `ssh2-sftp-client` module and I will investigate. Please note the next section on logging issues.

Note also that in the repository there are two useful directories. The first is the examples directory, which contain some examples of using `ssh2-sftp-client` to perform common tasks. A few minutes reviewing these examples can provide that additional bit of detail to help fix any problems you are encountering.

The second directory is the validation directory. I have some very simple scripts in this directory which perform basic tasks using only the `ssh2` modules (no `ssh2-sftp-client` module). These can be useful when trying to determine if the issue is with the underlying `ssh2` module or the `ssh2-sftp-client` wrapper module.


<a id="org39ed676"></a>

## Common Errors

There are some common errors people tend to make when using Promises or Async/Await. These are by far the most common problem found in issues logged against this module. Please check for some of these before logging your issue.


<a id="orge4fdace"></a>

### Not returning the promise in a `then()` block

All methods in `ssh2-sftp-client` return a Promise. This means methods are executed *asynchrnously*. When you call a method inside the `then()` block of a promise chain, it is critical that you return the Promise that call generates. Failing to do this will result in the `then()` block completing and your code starting execution of the next `then()`, `catch()` or `finally()` block before your promise has been fulfilled. For example, the following will not do what you expect

```javascript
sftp.connect(config)
  .then(() => {
    sftp.fastGet('foo.txt', 'bar.txt');
  }).then(rslt => {
    console.log(rslt);
    sftp.end();
  }).catch(e => {
    console.error(e.message);
  });
```

In the above code, the `sftp.end()` method will almost certainly be called before `sftp.fastGet()` has been fulfilled (unless the *foo.txt* file is really small!). In fact, the whole promise chain will complete and exit even before the `sftp.end()` call has been fulfilled. The correct code would be something like

```javascript
sftp.connect(config)
  .then(() => {
    return sftp.fastGet('foo.txt', 'bar.txt');
  }).then(rslt => {
    console.log(rslt);
    return sftp.end();
  }).catch(e => {
    console.error(e.message);
  });
```

Note the `return` statements. These ensure that the Promise returned by the client method is returned into the promise chain. It will be this promise the next block in the chain will wait on to be fulfilled before the next block is executed. Without the return statement, that block will return the default promise for that block, which essentially says *this block has been fulfilled*. What you really want is the promise which says *your sftp client method call has been fulfilled*.

A common symptom of this type of error is for file uploads or download to fail to complete or for data in those files to be truncated. What is happening is that the connection is being ended before the transfer has completed.


<a id="org613b5f0"></a>

### Mixing Promise Chains and Async/Await

Another common error is to mix Promise chains and async/await calls. This is rarely a great idea. While you can do this, it tends to create complicated and difficult to maintain code. Select one approach and stick with it. Both approaches are functionally equivalent, so there is no reason to mix up the two paradigms. My personal preference would be to use async/await as I think that is more *natural* for most developers. For example, the following is more complex and difficult to follow than necessary (and has a bug!)

```javascript
sftp.connect(config)
  .then(() => {
    return sftp.cwd();
  }).then(async (d) => {
    console.log(`Remote directory is ${d}`);
    try {
      await sftp.fastGet(`${d}/foo.txt`, `./bar.txt`);
    }.catch(e => {
      console.error(e.message);
    });
  }).catch(e => {
    console.error(e.message);
  }).finally(() => {
    sftp.end();
  });
```

The main bug in the above code is the `then()` block is not returning the Promise generated by the call to `sftp.fastGet()`. What it is actually returning is a fulfilled promise which says the `then()` block has been run (note that the await'ed promise is not being returned and is therefore outside the main Promise chain). As a result, the `finally()` block will be executed before the await promise has been fulfilled.

Using async/await inside the promise chain has created unnecessary complexity and leads to incorrect assumptions regarding how the code will execute. A quick glance at the code is likely to give the impression that execution will wait for the `sftp.fastGet()` call to be fulfilled before continuing. This is not the case. The code would be more clearly expressed as either

```javascript
sftp.connect(config)
  .then(() => {
    return sftp.cwd();
  }).then(d => {
    console.log(`remote dir ${d}`);
    return sftp.fastGet(`${d}/foot.txt`, 'bar.txt');
  }).catch(e => {
    console.error(e.message);
  }).finally(() => {
    return sftp.end();
  });
```

**or, using async/await**

```javascript
async function doSftp() {
  try {
    let sftp = await sftp.connect(conf);
    let d = await sftp.cwd();
    console.log(`remote dir is ${d}`);
    await sftp.fastGet(`${d}/foo.txt`, 'bat.txt');
  } catch (e) {
    console.error(e.message);
  } finally {
    await sftp.end();
  }
}
```


<a id="org3df8f9d"></a>

### Try/catch and Error Handlers

Another common error is to try and use a try/catch block to catch event signals, such as an error event. In general, you cannot use try/catch blocks for asynchronous code and expect errors to be caught by the `catch` block. Handling errors in asynchronous code is one of the key reasons we now have the Promise and async/await frameworks.

The basic problem is that the try/catch block will have completed execution before the asynchronous code has completed. If the asynchronous code has not completed, then there is a potential for it to raise an error. However, as the try/catch block has already completed, there is no *catch* waiting to catch the error. It will bubble up and probably result in your script exiting with an uncaught exception error.

Error events are essentially asynchronous code. You don't know when such events will fire. Therefore, you cannot use a try/catch block to catch such event errors. Even creating an error handler which then throws an exception won't help as the key problem is that your try/catch block has already executed. There are a number of alternative ways to deal with this situation. However, the key symptom is that you see occasional uncaught error exceptions that cause your script to exit abnormally despite having try/catch blocks in your script. What you need to do is look at your code and find where errors are raised asynchronously and use an event handler or some other mechanism to manage any errors raised.


<a id="org295cfa3"></a>

### Server Differences

Not all SFTP servers are the same. Like most standards, the SFTP protocol has some level of interpretation and allows different levels of compliance. This means there can be differences in behaviour between different servers and code which works with one server will not work the same with another. For example, the value returned by *realpath* for non-existent objects can differ significantly. Some servers will throw an error for a particular operation while others will just return null, some servers support concurrent operations (such as used by fastGet/fastPut) while others will not and of course, the text of error messages can vary significantly. In particular, we have noticed significant differences across different platforms. It is therefore advisable to do comprehensive testing when the SFTP server is moved to a new platform. This includes moving from to a cloud based service even if the underlying platform remains the same. I have noticed that some cloud platforms can generate unexpected events, possibly related to additional functionality or features associated with the cloud implementation. For example, it appears SFTP servers running under Azure will generate an error event when the connection is closed even when the client has requested the connection be terminated. The same SFTP server running natively on Windows does not appear to exhibit such behaviour.


<a id="org5ee2ce1"></a>

### Avoid Concurrent Operations

Technically, SFTP should be able to perform multiple operations concurrently. As node is single threaded, what we a really talking about is running multiple execution contexts as a pool where node will switch contexts when each context is blocked due to things like waiting on network data etc. However, I have found this to be extremely unreliable and of very little benefit from a performance perspective. My recommendation is to therefore avoid executing multiple requests over the same connection in parallel (for example, generating multiple `get()` promises and using something like `Promise.all()` to resolve them.

If you are going to try and perform concurrent operations, you need to test extensively and ensure you are using data which is large enough that context switching does occur (i.e. the request is not completed in a single run). Some SFTP servers will handle concurrent operations better than others.


<a id="orgec0b62a"></a>

## Debugging Support

You can add a `debug` property to the config object passed in to `connect()` to turn on debugging. This will generate quite a lot of output. The value of the property should be a function which accepts a single string argument. For example;

```javascript
config.debug = msg => {
  console.error(msg);
};

```

Enabling debugging can generate a lot of output. If you use console.error() as the output (as in the example above), you can redirect the output to a file using shell redirection e.g.

```shell
node script.js 2> debug.log

```

If you just want to see debug messages from `ssh2-sftp-client` and exclude debug messages from the underlying `ssh2` and `ssh2-streams` modules, you can filter based on messages which start with 'CLIENT' e.g.

```javascript
{
  debug: (msg) => {
    if (msg.startsWith('CLIENT')) {
      console.error(msg);
    }
  }
}
```


<a id="org41f81c7"></a>

# Logging Issues

Please log an issue for all bugs, questions, feature and enhancement requests. Please ensure you include the module version, node version and platform.

I am happy to try and help diagnose and fix any issues you encounter while using the `ssh2-sftp-client` module. However, I will only put in effort if you are prepared to put in the effort to provide the information necessary to reproduce the issue. Things which will help

-   Node version you are using
-   Version of ssh2-sftp-client you are using
-   Platform your client is running on (Linux, macOS, Windows)
-   Platform and software for the remote SFTP server when possible
-   Example of your code or a minimal script which reproduces the issue you are encountering. By far, the most common issue is incorrect use of the module API. Example code can usually result in such issues being resolved very quickly.

Perhaps the best assistance is a minimal reproducible example of the issue. Once the issue can be readily reproduced, it can usually be fixed very quickly.


<a id="orgd153e6d"></a>

# Pull Requests

Pull requests are always welcomed. However, please ensure your changes pass all tests and if you're adding a new feature, that tests for that feature are included. Likewise, for new features or enhancements, please include any relevant documentation updates.

**Note**: The `README.md` file is generated from the `README.org` file. Therefore, any documentation updates or fixes need to be made to the `README.org` file. This file is *tangled* using `Emacs` org mode. If you don't use Emacs or org-mode, don't be too concerned. The org-mode syntax is straight-forward and similar to *markdown*. I will verify any updates to `README.org` and generate a new `README.md` when necessary. The main point to note is that any changes made directly to `README.md` will not persist and will be lost when a new version is generated, so don't modify that file.

This module will adopt a standard semantic versioning policy. Please indicate in your pull request what level of change it represents i.e.

-   **Major:** Change to API or major change in functionality which will require an increase in major version number.

-   **Minor:** Minor change, enhancement or new feature which does not change existing API and will not break existing client code.

-   **Bug Fix:** No change to functionality or features. Simple fix of an existing bug.


<a id="org267dc2a"></a>

# Contributors

This module was initially written by jyu213. On August 23rd, 2019, theophilusx took over responsibility for maintaining this module. A number of other people have contributed to this module, but until now, this was not tracked. My intention is to credit anyone who contributes going forward.

Thanks to the following for their contributions -

-   **jyu213:** Original author
-   **theophilusx:** Current maintainer
-   **henrytk:** Documentation fix
-   **waldyrious:** Documentation fixes
-   **james-pellow:** Cleanup and fix for connect method logic
-   **jhorbulyk:** Contributed posixRename() functionality
-   **teenangst:** Contributed fix for error code 4 in stat() method
-   **kennylbj:** Contributed example of using a throttle stream to limit upload/download bandwidth.
-   **anton-erofeev:** Documentation fix
-   **Ladislav Jacho:** Contributed solution explanation for connections hanging when transferring larger files.
-   **Emma Milner:** Contributed fix for put() bug
-   **Witni Davis:** Contributed PR to fix put() RCE when using 'finish' rather than 'close' to resolve promise
-   **Maik Marschner:** Contributed fix for connect() not returning sftp object. Also included test to check for this regression in future.
-   **cakemasher:** Contributed fix for removeTempListeners().
