#!/bin/bash

# 太空大数据平台服务管理脚本
# 用于管理SFTP图片代理服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 默认配置
DEFAULT_PORT=3002
SERVICE_NAME="sftp-image-proxy"
PID_FILE=".sftp-proxy.pid"
LOG_FILE="sftp-proxy.log"

# 显示帮助信息
show_help() {
    echo "太空大数据平台服务管理脚本"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  start     启动SFTP图片代理服务"
    echo "  stop      停止SFTP图片代理服务"
    echo "  restart   重启SFTP图片代理服务"
    echo "  status    查看服务状态"
    echo "  logs      查看服务日志"
    echo "  health    检查服务健康状态"
    echo "  install   安装为系统服务"
    echo "  uninstall 卸载系统服务"
    echo ""
    echo "选项:"
    echo "  -p, --port PORT    指定端口 (默认: $DEFAULT_PORT)"
    echo "  -h, --help         显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start           # 启动服务"
    echo "  $0 start -p 3003   # 在端口3003启动服务"
    echo "  $0 status          # 查看服务状态"
    echo "  $0 logs            # 查看服务日志"
}

# 解析命令行参数
parse_args() {
    COMMAND=""
    PORT=$DEFAULT_PORT
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            start|stop|restart|status|logs|health|install|uninstall)
                COMMAND="$1"
                shift
                ;;
            -p|--port)
                PORT="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                echo -e "${RED}未知参数: $1${NC}"
                show_help
                exit 1
                ;;
        esac
    done
    
    if [ -z "$COMMAND" ]; then
        echo -e "${RED}错误: 请指定命令${NC}"
        show_help
        exit 1
    fi
}

# 检查PM2是否可用
check_pm2() {
    command -v pm2 &> /dev/null
}

# 获取服务PID
get_service_pid() {
    if check_pm2; then
        pm2 pid $SERVICE_NAME 2>/dev/null || echo ""
    else
        if [ -f "$PID_FILE" ]; then
            local pid=$(cat "$PID_FILE")
            if kill -0 "$pid" 2>/dev/null; then
                echo "$pid"
            else
                rm -f "$PID_FILE"
                echo ""
            fi
        else
            echo ""
        fi
    fi
}

# 检查服务是否运行
is_service_running() {
    local pid=$(get_service_pid)
    [ ! -z "$pid" ]
}

# 启动服务
start_service() {
    echo -e "${BLUE}启动SFTP图片代理服务...${NC}"
    
    if is_service_running; then
        echo -e "${YELLOW}服务已在运行中${NC}"
        return 0
    fi
    
    # 检查端口是否被占用
    if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${RED}端口 $PORT 已被占用${NC}"
        return 1
    fi
    
    # 确保生产配置文件存在
    if [ ! -f "sftp-image-proxy-server.prod.js" ]; then
        echo -e "${YELLOW}生产配置文件不存在，使用开发配置...${NC}"
        if [ ! -f "sftp-image-proxy-server.js" ]; then
            echo -e "${RED}错误: 未找到SFTP图片代理服务器文件${NC}"
            return 1
        fi
        SERVER_FILE="sftp-image-proxy-server.js"
    else
        SERVER_FILE="sftp-image-proxy-server.prod.js"
    fi
    
    if check_pm2; then
        # 使用PM2启动
        IMAGE_PROXY_PORT=$PORT pm2 start $SERVER_FILE --name $SERVICE_NAME
        pm2 save
        echo -e "${GREEN}✅ 服务已通过PM2启动${NC}"
    else
        # 使用nohup启动
        IMAGE_PROXY_PORT=$PORT nohup node $SERVER_FILE > $LOG_FILE 2>&1 &
        local pid=$!
        echo $pid > $PID_FILE
        echo -e "${GREEN}✅ 服务已启动 (PID: $pid)${NC}"
    fi
    
    # 等待服务启动
    sleep 2
    
    # 验证服务状态
    if is_service_running; then
        echo -e "${GREEN}✅ 服务启动成功${NC}"
        echo "   端口: $PORT"
        echo "   健康检查: http://localhost:$PORT/health"
    else
        echo -e "${RED}❌ 服务启动失败${NC}"
        return 1
    fi
}

# 停止服务
stop_service() {
    echo -e "${BLUE}停止SFTP图片代理服务...${NC}"
    
    if ! is_service_running; then
        echo -e "${YELLOW}服务未运行${NC}"
        return 0
    fi
    
    if check_pm2; then
        # 使用PM2停止
        pm2 stop $SERVICE_NAME
        pm2 delete $SERVICE_NAME
        echo -e "${GREEN}✅ 服务已通过PM2停止${NC}"
    else
        # 使用kill停止
        local pid=$(get_service_pid)
        if [ ! -z "$pid" ]; then
            kill -TERM "$pid" 2>/dev/null || true
            sleep 2
            if kill -0 "$pid" 2>/dev/null; then
                kill -KILL "$pid" 2>/dev/null || true
            fi
            rm -f "$PID_FILE"
            echo -e "${GREEN}✅ 服务已停止 (PID: $pid)${NC}"
        fi
    fi
}

# 重启服务
restart_service() {
    echo -e "${BLUE}重启SFTP图片代理服务...${NC}"
    stop_service
    sleep 1
    start_service
}

# 查看服务状态
show_status() {
    echo -e "${BLUE}SFTP图片代理服务状态:${NC}"
    echo ""
    
    if check_pm2; then
        # PM2状态
        if pm2 list | grep -q $SERVICE_NAME; then
            pm2 show $SERVICE_NAME
        else
            echo -e "${YELLOW}服务未在PM2中运行${NC}"
        fi
    else
        # 手动管理状态
        local pid=$(get_service_pid)
        if [ ! -z "$pid" ]; then
            echo -e "${GREEN}✅ 服务正在运行${NC}"
            echo "   PID: $pid"
            echo "   端口: $PORT"
            echo "   日志文件: $LOG_FILE"
        else
            echo -e "${RED}❌ 服务未运行${NC}"
        fi
    fi
    
    # 检查端口状态
    if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${GREEN}✅ 端口 $PORT 正在监听${NC}"
    else
        echo -e "${RED}❌ 端口 $PORT 未在监听${NC}"
    fi
}

# 查看日志
show_logs() {
    echo -e "${BLUE}SFTP图片代理服务日志:${NC}"
    echo ""
    
    if check_pm2; then
        pm2 logs $SERVICE_NAME --lines 50
    else
        if [ -f "$LOG_FILE" ]; then
            tail -f "$LOG_FILE"
        else
            echo -e "${YELLOW}日志文件不存在: $LOG_FILE${NC}"
        fi
    fi
}

# 健康检查
health_check() {
    echo -e "${BLUE}SFTP图片代理服务健康检查:${NC}"
    echo ""
    
    local health_url="http://localhost:$PORT/health"
    
    if curl -s "$health_url" > /dev/null; then
        echo -e "${GREEN}✅ 服务健康检查通过${NC}"
        echo ""
        echo "健康检查响应:"
        curl -s "$health_url" | python3 -m json.tool 2>/dev/null || curl -s "$health_url"
    else
        echo -e "${RED}❌ 服务健康检查失败${NC}"
        echo "请检查服务是否正在运行，端口是否正确"
    fi
}

# 安装为系统服务
install_system_service() {
    echo -e "${BLUE}安装SFTP图片代理为系统服务...${NC}"
    
    if [ "$EUID" -ne 0 ]; then
        echo -e "${RED}错误: 需要root权限安装系统服务${NC}"
        echo "请使用: sudo $0 install"
        return 1
    fi
    
    # 复制服务文件
    if [ -f "sftp-image-proxy.service" ]; then
        cp sftp-image-proxy.service /etc/systemd/system/
        
        # 更新工作目录
        sed -i "s|WorkingDirectory=.*|WorkingDirectory=$(pwd)|g" /etc/systemd/system/sftp-image-proxy.service
        
        # 重新加载systemd
        systemctl daemon-reload
        systemctl enable sftp-image-proxy.service
        
        echo -e "${GREEN}✅ 系统服务安装成功${NC}"
        echo ""
        echo "管理命令:"
        echo "  启动: sudo systemctl start sftp-image-proxy"
        echo "  停止: sudo systemctl stop sftp-image-proxy"
        echo "  状态: sudo systemctl status sftp-image-proxy"
        echo "  日志: sudo journalctl -u sftp-image-proxy -f"
    else
        echo -e "${RED}错误: 未找到服务文件 sftp-image-proxy.service${NC}"
        return 1
    fi
}

# 卸载系统服务
uninstall_system_service() {
    echo -e "${BLUE}卸载SFTP图片代理系统服务...${NC}"
    
    if [ "$EUID" -ne 0 ]; then
        echo -e "${RED}错误: 需要root权限卸载系统服务${NC}"
        echo "请使用: sudo $0 uninstall"
        return 1
    fi
    
    # 停止并禁用服务
    systemctl stop sftp-image-proxy.service 2>/dev/null || true
    systemctl disable sftp-image-proxy.service 2>/dev/null || true
    
    # 删除服务文件
    rm -f /etc/systemd/system/sftp-image-proxy.service
    
    # 重新加载systemd
    systemctl daemon-reload
    
    echo -e "${GREEN}✅ 系统服务卸载成功${NC}"
}

# 主函数
main() {
    parse_args "$@"
    
    case $COMMAND in
        start)
            start_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            restart_service
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs
            ;;
        health)
            health_check
            ;;
        install)
            install_system_service
            ;;
        uninstall)
            uninstall_system_service
            ;;
        *)
            echo -e "${RED}未知命令: $COMMAND${NC}"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
