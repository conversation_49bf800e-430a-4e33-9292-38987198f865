# 🚀 快速部署指南 - 解决图片代理问题

## 问题分析
您遇到的 `ERR_EMPTY_RESPONSE` 错误是因为前端配置的图片代理地址与服务器地址不匹配。

## 解决步骤

### 1. 重新运行部署脚本 (推荐方式)

在服务器上执行以下命令：

```bash
# 进入项目目录
cd /path/to/your/project

# 运行部署脚本，这次输入正确的API地址
./fix-api-config-with-proxy.sh
```

**重要：** 当脚本询问后端API地址时，请输入您的实际服务器地址，例如：
```bash
🌐 请输入您的后端API地址: http://*************:3001
🖼️  请输入SFTP图片代理端口: 3002
```

### 2. 验证配置

部署完成后，检查配置是否正确：

```bash
# 检查前端图片服务配置
grep "IMAGE_PROXY_BASE_URL" frontend/src/services/imageService.ts

# 应该显示类似：
# const IMAGE_PROXY_BASE_URL = 'http://*************:3002';
```

### 3. 重启服务

```bash
# 重启图片代理服务
pm2 restart sftp-image-proxy

# 查看服务状态
pm2 status

# 查看服务日志
pm2 logs sftp-image-proxy --lines 20
```

### 4. 测试服务

```bash
# 测试健康检查
curl http://localhost:3002/health

# 测试SFTP连接
curl http://localhost:3002/test-connection

# 从外部测试（如果有公网IP）
curl http://*************:3002/health
```

### 5. 重启Web服务器

```bash
# 重启Nginx（如果使用）
sudo systemctl restart nginx

# 或重启Apache
sudo systemctl restart apache2
```

## 部署脚本的工作原理

`fix-api-config-with-proxy.sh` 脚本会：

1. **动态计算图片代理URL**：
   - 如果API地址是localhost → 图片代理也用localhost
   - 如果API地址是远程服务器 → 图片代理用相同的域名/IP

2. **自动更新配置**：
   - 备份原配置文件
   - 更新 `frontend/src/services/imageService.ts`
   - 重新构建前端项目

3. **启动服务**：
   - 停止旧的代理服务
   - 启动新的SFTP图片代理服务

## 验证部署成功

部署成功后，您应该看到：

1. **PM2状态显示服务运行正常**
2. **前端不再显示图片加载错误**
3. **浏览器网络请求成功返回图片数据**

## 如果仍有问题

如果问题仍然存在，请检查：

1. **防火墙设置**：确保端口3002已开放
2. **SFTP连接**：确认能连接到**************
3. **服务日志**：查看具体错误信息

```bash
# 检查防火墙
sudo ufw status

# 检查端口
netstat -tlnp | grep 3002

# 查看详细日志
pm2 logs sftp-image-proxy --err --lines 50
```

---

**完成这些步骤后，您的图片代理服务应该能正常工作了！** 🎉 