#!/bin/bash

# API配置修复脚本 (增强版 - 包含SFTP图片代理)
# 解决前端API地址错误导致的页面刷新循环问题，并启动图片代理服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔧 修复API配置问题 + 启动图片代理${NC}"
echo "=========================================="

# 检查当前目录是否为项目根目录
if [ ! -f "frontend/src/services/api.ts" ]; then
    echo -e "${RED}❌ 错误: 请在项目根目录下运行此脚本${NC}"
    echo "当前目录: $(pwd)"
    echo "请确保目录中包含 frontend/src/services/api.ts 文件"
    exit 1
fi

# 检查SFTP图片代理文件
if [ ! -f "sftp-image-proxy-server.js" ]; then
    echo -e "${RED}❌ 错误: 未找到SFTP图片代理服务器文件${NC}"
    echo "请确保 sftp-image-proxy-server.js 文件存在"
    exit 1
fi

# 获取用户输入的API地址
read -p "🌐 请输入您的后端API地址 (例如: http://api.example.com:3001): " API_URL

# 验证输入
if [ -z "$API_URL" ]; then
    echo -e "${RED}❌ 错误: API地址不能为空${NC}"
    exit 1
fi

# 获取图片代理端口
read -p "🖼️  请输入SFTP图片代理端口 (默认: 3002): " IMAGE_PROXY_PORT
IMAGE_PROXY_PORT=${IMAGE_PROXY_PORT:-3002}

echo ""
echo -e "${BLUE}📋 配置信息:${NC}"
echo "   后端API地址: $API_URL"
echo "   图片代理端口: $IMAGE_PROXY_PORT"
echo ""

# 确认配置
read -p "确认以上配置并继续? (y/n): " CONFIRM
if [[ "$CONFIRM" != "y" && "$CONFIRM" != "Y" ]]; then
    echo "操作已取消"
    exit 0
fi

echo ""
echo -e "${YELLOW}⚙️ 开始配置和部署...${NC}"

# 1. 备份原API配置文件
echo "📋 备份原API配置文件..."
cp frontend/src/services/api.ts frontend/src/services/api.ts.backup.$(date +%Y%m%d_%H%M%S)

# 2. 修改API配置文件
echo "⚙️ 修改API配置文件..."
sed -i.tmp "s|export const API_BASE_URL = 'http://localhost:3001';|export const API_BASE_URL = '$API_URL';|g" frontend/src/services/api.ts
rm -f frontend/src/services/api.ts.tmp

# 3. 更新前端图片服务配置
echo "🖼️  更新前端图片服务配置..."
if [ -f "frontend/src/services/imageService.ts" ]; then
    cp frontend/src/services/imageService.ts frontend/src/services/imageService.ts.backup.$(date +%Y%m%d_%H%M%S)
    
    # 计算图片代理URL
    PROXY_URL="http://localhost:${IMAGE_PROXY_PORT}"
    if [[ "$API_URL" != *"localhost"* ]]; then
        DOMAIN=$(echo "$API_URL" | sed 's|http[s]*://||' | sed 's|:[0-9]*||')
        PROXY_URL="http://${DOMAIN}:${IMAGE_PROXY_PORT}"
    fi
    
    # 更新图片代理基础URL (支持不同的端口号)
    sed -i.tmp "s|const IMAGE_PROXY_BASE_URL = 'http://localhost:[0-9]*';|const IMAGE_PROXY_BASE_URL = '${PROXY_URL}';|g" frontend/src/services/imageService.ts
    sed -i.tmp "s|const IMAGE_PROXY_BASE_URL = 'http://localhost:3002';|const IMAGE_PROXY_BASE_URL = '${PROXY_URL}';|g" frontend/src/services/imageService.ts
    rm -f frontend/src/services/imageService.ts.tmp
    
    echo -e "${GREEN}✅ 图片服务配置已更新: $PROXY_URL${NC}"
    echo -e "${BLUE}ℹ️  前端将通过 $PROXY_URL 访问图片代理服务${NC}"
fi

# 4. 安装依赖 (如果需要)
if [ ! -d "node_modules" ] || [ ! -d "frontend/node_modules" ]; then
    echo "📦 安装项目依赖..."
    npm install
    cd frontend && npm install && cd ..
fi

# 5. 构建前端项目
echo "🔨 开始重新构建项目..."
cd frontend
npm run build
cd ..

# 6. 停止现有的图片代理服务
echo "🛑 停止现有的图片代理服务..."
if command -v pm2 &> /dev/null; then
    pm2 stop sftp-image-proxy 2>/dev/null || true
    pm2 delete sftp-image-proxy 2>/dev/null || true
fi

# 停止可能占用端口的进程
PID=$(lsof -ti:${IMAGE_PROXY_PORT} 2>/dev/null || true)
if [ ! -z "$PID" ]; then
    echo "终止占用端口 ${IMAGE_PROXY_PORT} 的进程 $PID..."
    kill -TERM $PID 2>/dev/null || true
    sleep 2
    kill -KILL $PID 2>/dev/null || true
fi

# 7. 创建生产环境配置
echo "⚙️ 创建生产环境图片代理配置..."
cat > sftp-image-proxy-server.prod.js << 'EOF'
const express = require('express');
const cors = require('cors');
const SftpClient = require('ssh2-sftp-client');
const path = require('path');

const app = express();
const PORT = process.env.IMAGE_PROXY_PORT || 3002;

// SFTP服务器配置
const SFTP_CONFIG = {
  host: '**************',
  port: 22,
  username: 'yulingjing',
  password: 'readnewsjpg123',
  readyTimeout: 20000,
  retries: 3
};

// 内存缓存配置
const imageCache = new Map();
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24小时缓存
const MAX_CACHE_SIZE = 200; // 生产环境增加缓存

// 启用CORS
app.use(cors({
  origin: true,
  credentials: true
}));

// 清理过期缓存
function cleanExpiredCache() {
  const now = Date.now();
  for (const [key, value] of imageCache.entries()) {
    if (now - value.timestamp > CACHE_DURATION) {
      imageCache.delete(key);
      console.log(`[Cache] 清理过期缓存: ${key}`);
    }
  }
}

setInterval(cleanExpiredCache, 60 * 60 * 1000);

// 获取图片的路由
app.get('/image', async (req, res) => {
  const { path: imagePath } = req.query;
  
  if (!imagePath) {
    return res.status(400).json({ 
      error: 'Missing image path parameter',
      message: '缺少图片路径参数'
    });
  }

  console.log(`[SFTP Proxy] 请求图片: ${imagePath}`);

  // 检查缓存
  const cacheKey = imagePath;
  const cachedImage = imageCache.get(cacheKey);
  
  if (cachedImage && (Date.now() - cachedImage.timestamp < CACHE_DURATION)) {
    console.log(`[Cache] 命中缓存: ${imagePath}`);
    res.set({
      'Content-Type': cachedImage.contentType,
      'Cache-Control': 'public, max-age=86400',
      'X-Cache': 'HIT'
    });
    return res.send(cachedImage.data);
  }

  const sftp = new SftpClient();

  try {
    await sftp.connect(SFTP_CONFIG);
    const imageBuffer = await sftp.get(imagePath);
    
    if (!imageBuffer || imageBuffer.length === 0) {
      throw new Error('图片文件为空或不存在');
    }

    const ext = path.extname(imagePath).toLowerCase();
    let contentType = 'image/jpeg';
    
    switch (ext) {
      case '.jpg':
      case '.jpeg':
        contentType = 'image/jpeg';
        break;
      case '.png':
        contentType = 'image/png';
        break;
      case '.gif':
        contentType = 'image/gif';
        break;
      case '.webp':
        contentType = 'image/webp';
        break;
    }

    if (imageCache.size < MAX_CACHE_SIZE) {
      imageCache.set(cacheKey, {
        data: imageBuffer,
        contentType,
        timestamp: Date.now()
      });
    }

    res.set({
      'Content-Type': contentType,
      'Content-Length': imageBuffer.length,
      'Cache-Control': 'public, max-age=86400',
      'X-Cache': 'MISS'
    });

    res.send(imageBuffer);

  } catch (error) {
    console.error(`[SFTP] 下载图片失败: ${imagePath}`, error.message);
    
    res.status(404).json({
      error: 'Image not found',
      message: '图片未找到或下载失败',
      path: imagePath,
      details: error.message
    });
  } finally {
    try {
      await sftp.end();
    } catch (closeError) {
      console.error(`[SFTP] 关闭连接时出错:`, closeError.message);
    }
  }
});

// 健康检查路由
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    service: 'SFTP Image Proxy (Production)',
    protocol: 'SFTP',
    cache: {
      size: imageCache.size,
      maxSize: MAX_CACHE_SIZE
    },
    timestamp: new Date().toISOString()
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🖼️  SFTP图片代理服务器启动成功 (生产环境)`);
  console.log(`📡 监听端口: ${PORT}`);
  console.log(`🔗 SFTP服务器: ${SFTP_CONFIG.host}:${SFTP_CONFIG.port}`);
  console.log(`🌐 健康检查: http://localhost:${PORT}/health`);
});

process.on('SIGINT', () => {
  console.log('\n🛑 收到关闭信号，正在优雅关闭服务器...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 收到终止信号，正在优雅关闭服务器...');
  process.exit(0);
});
EOF

# 8. 启动SFTP图片代理服务
echo "🚀 启动SFTP图片代理服务..."

if command -v pm2 &> /dev/null; then
    # 使用PM2启动
    IMAGE_PROXY_PORT=$IMAGE_PROXY_PORT pm2 start sftp-image-proxy-server.prod.js --name sftp-image-proxy
    pm2 save
    echo -e "${GREEN}✅ SFTP图片代理服务已通过PM2启动${NC}"
else
    # 使用nohup后台启动
    IMAGE_PROXY_PORT=$IMAGE_PROXY_PORT nohup node sftp-image-proxy-server.prod.js > sftp-proxy.log 2>&1 &
    PROXY_PID=$!
    echo $PROXY_PID > .sftp-proxy.pid
    echo -e "${GREEN}✅ SFTP图片代理服务已启动 (PID: $PROXY_PID)${NC}"
fi

# 9. 等待服务启动并测试
echo "⏳ 等待服务启动..."
sleep 3

# 测试图片代理服务
if curl -s "http://localhost:${IMAGE_PROXY_PORT}/health" > /dev/null; then
    echo -e "${GREEN}✅ SFTP图片代理服务运行正常${NC}"
else
    echo -e "${YELLOW}⚠️  SFTP图片代理服务可能启动失败，请检查日志${NC}"
fi

echo ""
echo -e "${GREEN}✅ 修复完成！${NC}"
echo ""
echo -e "${BLUE}📋 修复说明:${NC}"
echo "  1. 已将API基础URL从 'http://localhost:3001' 更改为 '$API_URL'"
echo "  2. 已配置SFTP图片代理服务，端口: $IMAGE_PROXY_PORT"
echo "  3. 已重新构建项目，生成新的生产版本"
echo "  4. 原配置文件已备份"
echo ""
echo -e "${BLUE}🖼️  图片代理服务信息:${NC}"
echo "  健康检查: http://localhost:${IMAGE_PROXY_PORT}/health"
echo "  缓存状态: http://localhost:${IMAGE_PROXY_PORT}/cache/status"
echo ""
echo -e "${BLUE}🔄 现在需要重启Web服务器:${NC}"
echo "  sudo systemctl restart nginx"
echo ""
echo -e "${BLUE}🧪 测试建议:${NC}"
echo "  1. 清除浏览器缓存"
echo "  2. 重新访问网站"
echo "  3. 检查新闻页面图片显示效果"
echo "  4. 检查浏览器开发者工具的网络请求"
echo "  5. 确保后端API服务器在 $API_URL 正常运行"
echo ""
if command -v pm2 &> /dev/null; then
    echo -e "${BLUE}📊 PM2管理命令:${NC}"
    echo "  查看状态: pm2 status"
    echo "  查看日志: pm2 logs sftp-image-proxy"
    echo "  重启服务: pm2 restart sftp-image-proxy"
    echo "  停止服务: pm2 stop sftp-image-proxy"
else
    echo -e "${BLUE}📊 服务管理:${NC}"
    echo "  查看日志: tail -f sftp-proxy.log"
    echo "  停止服务: kill \$(cat .sftp-proxy.pid)"
fi
