#!/bin/bash

# 太空大数据平台部署脚本 (包含SFTP图片代理)
# 自动配置API地址、构建项目并启动SFTP图片代理服务

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示标题
echo "🚀 太空大数据平台部署脚本"
echo "================================"
echo "功能: API配置 + 前端构建 + SFTP图片代理"
echo ""

# 检查当前目录是否为项目根目录
check_project_directory() {
    log_info "检查项目目录结构..."
    
    if [ ! -f "frontend/src/services/api.ts" ]; then
        log_error "请在项目根目录下运行此脚本"
        log_error "当前目录: $(pwd)"
        log_error "请确保目录中包含 frontend/src/services/api.ts 文件"
        exit 1
    fi
    
    if [ ! -f "sftp-image-proxy-server.js" ]; then
        log_error "未找到SFTP图片代理服务器文件"
        log_error "请确保 sftp-image-proxy-server.js 文件存在"
        exit 1
    fi
    
    log_success "项目目录结构检查通过"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装，请先安装 npm"
        exit 1
    fi
    
    # 检查pm2 (用于生产环境进程管理)
    if ! command -v pm2 &> /dev/null; then
        log_warning "pm2 未安装，建议安装用于生产环境进程管理"
        log_info "安装命令: npm install -g pm2"
    fi
    
    local node_version=$(node --version)
    local npm_version=$(npm --version)
    log_success "依赖检查完成 - Node.js: $node_version, npm: $npm_version"
}

# 获取配置信息
get_configuration() {
    log_info "获取部署配置..."
    
    # 获取后端API地址
    while true; do
        read -p "🌐 请输入您的后端API地址 (例如: http://api.example.com:3001): " API_URL
        
        if [ -z "$API_URL" ]; then
            log_error "API地址不能为空，请重新输入"
            continue
        fi
        
        # 简单的URL格式验证
        if [[ ! "$API_URL" =~ ^https?:// ]]; then
            log_error "API地址格式不正确，请以 http:// 或 https:// 开头"
            continue
        fi
        
        break
    done
    
    # 获取图片代理端口
    read -p "🖼️  请输入SFTP图片代理端口 (默认: 3002): " IMAGE_PROXY_PORT
    IMAGE_PROXY_PORT=${IMAGE_PROXY_PORT:-3002}
    
    # 获取SFTP服务器配置
    echo ""
    log_info "SFTP图片服务器配置 (当前默认配置):"
    echo "   主机: **************"
    echo "   端口: 22"
    echo "   用户: yulingjing"
    
    read -p "是否使用默认SFTP配置? (y/n, 默认: y): " USE_DEFAULT_SFTP
    USE_DEFAULT_SFTP=${USE_DEFAULT_SFTP:-y}
    
    if [[ "$USE_DEFAULT_SFTP" != "y" && "$USE_DEFAULT_SFTP" != "Y" ]]; then
        read -p "SFTP主机地址: " SFTP_HOST
        read -p "SFTP端口 (默认: 22): " SFTP_PORT
        read -p "SFTP用户名: " SFTP_USER
        read -s -p "SFTP密码: " SFTP_PASSWORD
        echo ""
        
        SFTP_PORT=${SFTP_PORT:-22}
    else
        SFTP_HOST="**************"
        SFTP_PORT="22"
        SFTP_USER="yulingjing"
        SFTP_PASSWORD="readnewsjpg123"
    fi
    
    # 确认配置
    echo ""
    log_info "部署配置确认:"
    echo "   后端API地址: $API_URL"
    echo "   图片代理端口: $IMAGE_PROXY_PORT"
    echo "   SFTP服务器: $SFTP_HOST:$SFTP_PORT"
    echo "   SFTP用户: $SFTP_USER"
    echo ""
    
    read -p "确认以上配置并继续部署? (y/n): " CONFIRM
    if [[ "$CONFIRM" != "y" && "$CONFIRM" != "Y" ]]; then
        log_info "部署已取消"
        exit 0
    fi
}

# 安装依赖
install_dependencies() {
    log_info "安装项目依赖..."
    
    # 安装根目录依赖 (包含SFTP相关包)
    if [ ! -d "node_modules" ]; then
        log_info "安装根目录依赖..."
        npm install
    else
        log_info "根目录依赖已存在，跳过安装"
    fi
    
    # 安装前端依赖
    if [ ! -d "frontend/node_modules" ]; then
        log_info "安装前端依赖..."
        cd frontend && npm install && cd ..
    else
        log_info "前端依赖已存在，跳过安装"
    fi
    
    log_success "依赖安装完成"
}

# 配置API地址
configure_api() {
    log_info "配置API地址..."
    
    # 备份原文件
    if [ -f "frontend/src/services/api.ts" ]; then
        cp frontend/src/services/api.ts frontend/src/services/api.ts.backup.$(date +%Y%m%d_%H%M%S)
        log_info "原API配置已备份"
    fi
    
    # 修改API配置文件
    sed -i.tmp "s|export const API_BASE_URL = 'http://localhost:3001';|export const API_BASE_URL = '$API_URL';|g" frontend/src/services/api.ts
    rm -f frontend/src/services/api.ts.tmp
    
    log_success "API配置已更新: $API_URL"
}

# 配置图片代理服务
configure_image_proxy() {
    log_info "配置SFTP图片代理服务..."
    
    # 备份原文件
    if [ -f "sftp-image-proxy-server.js" ]; then
        cp sftp-image-proxy-server.js sftp-image-proxy-server.js.backup.$(date +%Y%m%d_%H%M%S)
        log_info "原图片代理配置已备份"
    fi
    
    # 创建生产环境配置文件
    cat > sftp-image-proxy-server.prod.js << EOF
const express = require('express');
const cors = require('cors');
const SftpClient = require('ssh2-sftp-client');
const path = require('path');

const app = express();
const PORT = process.env.IMAGE_PROXY_PORT || ${IMAGE_PROXY_PORT};

// SFTP服务器配置 (生产环境)
const SFTP_CONFIG = {
  host: '${SFTP_HOST}',
  port: ${SFTP_PORT},
  username: '${SFTP_USER}',
  password: '${SFTP_PASSWORD}',
  readyTimeout: 20000,
  retries: 3
};

// 内存缓存配置
const imageCache = new Map();
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24小时缓存
const MAX_CACHE_SIZE = 200; // 生产环境增加到200张图片

// 启用CORS (生产环境配置)
app.use(cors({
  origin: true, // 允许所有来源 (生产环境可根据需要限制)
  credentials: true
}));

// 清理过期缓存
function cleanExpiredCache() {
  const now = Date.now();
  for (const [key, value] of imageCache.entries()) {
    if (now - value.timestamp > CACHE_DURATION) {
      imageCache.delete(key);
      console.log(\`[Cache] 清理过期缓存: \${key}\`);
    }
  }
}

// 定期清理缓存
setInterval(cleanExpiredCache, 60 * 60 * 1000); // 每小时清理一次

// 获取图片的路由
app.get('/image', async (req, res) => {
  const { path: imagePath } = req.query;
  
  if (!imagePath) {
    return res.status(400).json({ 
      error: 'Missing image path parameter',
      message: '缺少图片路径参数'
    });
  }

  console.log(\`[SFTP Proxy] 请求图片: \${imagePath}\`);

  // 检查缓存
  const cacheKey = imagePath;
  const cachedImage = imageCache.get(cacheKey);
  
  if (cachedImage && (Date.now() - cachedImage.timestamp < CACHE_DURATION)) {
    console.log(\`[Cache] 命中缓存: \${imagePath}\`);
    res.set({
      'Content-Type': cachedImage.contentType,
      'Cache-Control': 'public, max-age=86400',
      'X-Cache': 'HIT'
    });
    return res.send(cachedImage.data);
  }

  // 创建SFTP客户端
  const sftp = new SftpClient();

  try {
    // 连接到SFTP服务器
    await sftp.connect(SFTP_CONFIG);
    
    // 下载图片
    const imageBuffer = await sftp.get(imagePath);
    
    if (!imageBuffer || imageBuffer.length === 0) {
      throw new Error('图片文件为空或不存在');
    }

    // 确定内容类型
    const ext = path.extname(imagePath).toLowerCase();
    let contentType = 'image/jpeg';
    
    switch (ext) {
      case '.jpg':
      case '.jpeg':
        contentType = 'image/jpeg';
        break;
      case '.png':
        contentType = 'image/png';
        break;
      case '.gif':
        contentType = 'image/gif';
        break;
      case '.webp':
        contentType = 'image/webp';
        break;
    }

    // 缓存图片
    if (imageCache.size < MAX_CACHE_SIZE) {
      imageCache.set(cacheKey, {
        data: imageBuffer,
        contentType,
        timestamp: Date.now()
      });
    }

    // 设置响应头
    res.set({
      'Content-Type': contentType,
      'Content-Length': imageBuffer.length,
      'Cache-Control': 'public, max-age=86400',
      'X-Cache': 'MISS'
    });

    res.send(imageBuffer);

  } catch (error) {
    console.error(\`[SFTP] 下载图片失败: \${imagePath}\`, error.message);
    
    res.status(404).json({
      error: 'Image not found',
      message: '图片未找到或下载失败',
      path: imagePath,
      details: error.message
    });
  } finally {
    try {
      await sftp.end();
    } catch (closeError) {
      console.error(\`[SFTP] 关闭连接时出错:\`, closeError.message);
    }
  }
});

// 健康检查路由
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    service: 'SFTP Image Proxy (Production)',
    protocol: 'SFTP',
    server: \`\${SFTP_CONFIG.host}:\${SFTP_CONFIG.port}\`,
    cache: {
      size: imageCache.size,
      maxSize: MAX_CACHE_SIZE
    },
    timestamp: new Date().toISOString()
  });
});

// 缓存状态路由
app.get('/cache/status', (req, res) => {
  const cacheEntries = Array.from(imageCache.entries()).map(([key, value]) => ({
    path: key,
    size: value.data.length,
    contentType: value.contentType,
    cached: new Date(value.timestamp).toISOString(),
    age: Date.now() - value.timestamp
  }));

  res.json({
    totalEntries: imageCache.size,
    maxSize: MAX_CACHE_SIZE,
    entries: cacheEntries
  });
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('[Server Error]', error);
  res.status(500).json({
    error: 'Internal server error',
    message: '服务器内部错误'
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(\`🖼️  SFTP图片代理服务器启动成功 (生产环境)\`);
  console.log(\`📡 监听端口: \${PORT}\`);
  console.log(\`🔗 SFTP服务器: \${SFTP_CONFIG.host}:\${SFTP_CONFIG.port}\`);
  console.log(\`👤 用户名: \${SFTP_CONFIG.username}\`);
  console.log(\`💾 缓存配置: 最大\${MAX_CACHE_SIZE}张图片，\${CACHE_DURATION / 1000 / 60 / 60}小时过期\`);
  console.log(\`🌐 健康检查: http://localhost:\${PORT}/health\`);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\\n🛑 收到关闭信号，正在优雅关闭服务器...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\\n🛑 收到终止信号，正在优雅关闭服务器...');
  process.exit(0);
});
EOF

    log_success "SFTP图片代理配置已生成: sftp-image-proxy-server.prod.js"
}

# 更新前端图片服务配置
update_frontend_image_service() {
    log_info "更新前端图片服务配置..."
    
    # 备份原文件
    if [ -f "frontend/src/services/imageService.ts" ]; then
        cp frontend/src/services/imageService.ts frontend/src/services/imageService.ts.backup.$(date +%Y%m%d_%H%M%S)
    fi
    
    # 更新图片代理URL为生产环境地址
    local PROXY_URL="http://localhost:${IMAGE_PROXY_PORT}"
    
    # 如果API_URL不是localhost，则使用相同的域名
    if [[ "$API_URL" != *"localhost"* ]]; then
        local DOMAIN=$(echo "$API_URL" | sed 's|http[s]*://||' | sed 's|:[0-9]*||')
        PROXY_URL="http://${DOMAIN}:${IMAGE_PROXY_PORT}"
    fi
    
    sed -i.tmp "s|const IMAGE_PROXY_BASE_URL = 'http://localhost:3002';|const IMAGE_PROXY_BASE_URL = '${PROXY_URL}';|g" frontend/src/services/imageService.ts
    rm -f frontend/src/services/imageService.ts.tmp
    
    log_success "前端图片服务配置已更新: $PROXY_URL"
}

# 构建前端项目
build_frontend() {
    log_info "构建前端项目..."
    
    cd frontend
    npm run build
    cd ..
    
    log_success "前端项目构建完成"
}

# 停止现有服务
stop_existing_services() {
    log_info "停止现有服务..."
    
    # 停止可能运行的图片代理服务
    if command -v pm2 &> /dev/null; then
        pm2 stop sftp-image-proxy 2>/dev/null || true
        pm2 delete sftp-image-proxy 2>/dev/null || true
    fi
    
    # 停止可能占用端口的进程
    local pid=$(lsof -ti:${IMAGE_PROXY_PORT} 2>/dev/null || true)
    if [ ! -z "$pid" ]; then
        log_warning "端口 ${IMAGE_PROXY_PORT} 被进程 $pid 占用，正在终止..."
        kill -TERM $pid 2>/dev/null || true
        sleep 2
        kill -KILL $pid 2>/dev/null || true
    fi
    
    log_success "现有服务已停止"
}

# 启动SFTP图片代理服务
start_image_proxy() {
    log_info "启动SFTP图片代理服务..."
    
    if command -v pm2 &> /dev/null; then
        # 使用PM2启动 (推荐用于生产环境)
        pm2 start sftp-image-proxy-server.prod.js --name sftp-image-proxy
        pm2 save
        log_success "SFTP图片代理服务已通过PM2启动"
        log_info "管理命令:"
        echo "   查看状态: pm2 status"
        echo "   查看日志: pm2 logs sftp-image-proxy"
        echo "   重启服务: pm2 restart sftp-image-proxy"
        echo "   停止服务: pm2 stop sftp-image-proxy"
    else
        # 使用nohup后台启动
        nohup node sftp-image-proxy-server.prod.js > sftp-proxy.log 2>&1 &
        local proxy_pid=$!
        echo $proxy_pid > .sftp-proxy.pid
        log_success "SFTP图片代理服务已启动 (PID: $proxy_pid)"
        log_info "管理命令:"
        echo "   查看日志: tail -f sftp-proxy.log"
        echo "   停止服务: kill \$(cat .sftp-proxy.pid)"
    fi
}

# 测试服务
test_services() {
    log_info "测试服务状态..."
    
    # 等待服务启动
    sleep 3
    
    # 测试图片代理服务
    if curl -s "http://localhost:${IMAGE_PROXY_PORT}/health" > /dev/null; then
        log_success "SFTP图片代理服务运行正常"
    else
        log_error "SFTP图片代理服务启动失败"
        return 1
    fi
    
    log_success "所有服务测试通过"
}

# 显示部署结果
show_deployment_result() {
    echo ""
    echo "🎉 部署完成！"
    echo "=============="
    echo ""
    log_success "部署摘要:"
    echo "   ✅ 后端API地址: $API_URL"
    echo "   ✅ SFTP图片代理: http://localhost:${IMAGE_PROXY_PORT}"
    echo "   ✅ 前端项目已构建"
    echo "   ✅ 图片代理服务已启动"
    echo ""
    log_info "服务地址:"
    echo "   🌐 图片代理健康检查: http://localhost:${IMAGE_PROXY_PORT}/health"
    echo "   📊 图片代理缓存状态: http://localhost:${IMAGE_PROXY_PORT}/cache/status"
    echo ""
    log_info "下一步操作:"
    echo "   1. 重启Web服务器: sudo systemctl restart nginx"
    echo "   2. 清除浏览器缓存"
    echo "   3. 访问网站测试功能"
    echo "   4. 检查新闻页面图片显示效果"
    echo ""
    log_warning "注意事项:"
    echo "   - 确保防火墙允许端口 ${IMAGE_PROXY_PORT}"
    echo "   - 确保SFTP服务器 ${SFTP_HOST} 可访问"
    echo "   - 建议设置图片代理服务开机自启动"
}

# 主函数
main() {
    check_project_directory
    check_dependencies
    get_configuration
    install_dependencies
    configure_api
    configure_image_proxy
    update_frontend_image_service
    build_frontend
    stop_existing_services
    start_image_proxy
    test_services
    show_deployment_result
}

# 错误处理
trap 'log_error "部署过程中发生错误，请检查上述日志"; exit 1' ERR

# 运行主函数
main "$@"
