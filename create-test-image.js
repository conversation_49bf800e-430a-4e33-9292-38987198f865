#!/usr/bin/env node

/**
 * 创建测试图片并上传到SFTP服务器
 */

const SftpClient = require('ssh2-sftp-client');
const fs = require('fs');

// SFTP服务器配置
const SFTP_CONFIG = {
  host: '**************',
  port: 22,
  username: 'yulingjing',
  password: 'readnewsjpg123',
  readyTimeout: 20000,
  retries: 3
};

// 创建一个简单的测试图片（1x1像素的JPEG）
function createTestImage() {
  // 最小的JPEG文件头和数据
  const jpegData = Buffer.from([
    0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
    0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xFF, 0xDB, 0x00, 0x43,
    0x00, 0x08, 0x06, 0x06, 0x07, 0x06, 0x05, 0x08, 0x07, 0x07, 0x07, 0x09,
    0x09, 0x08, 0x0A, 0x0C, 0x14, 0x0D, 0x0C, 0x0B, 0x0B, 0x0C, 0x19, 0x12,
    0x13, 0x0F, 0x14, 0x1D, 0x1A, 0x1F, 0x1E, 0x1D, 0x1A, 0x1C, 0x1C, 0x20,
    0x24, 0x2E, 0x27, 0x20, 0x22, 0x2C, 0x23, 0x1C, 0x1C, 0x28, 0x37, 0x29,
    0x2C, 0x30, 0x31, 0x34, 0x34, 0x34, 0x1F, 0x27, 0x39, 0x3D, 0x38, 0x32,
    0x3C, 0x2E, 0x33, 0x34, 0x32, 0xFF, 0xC0, 0x00, 0x11, 0x08, 0x00, 0x01,
    0x00, 0x01, 0x01, 0x01, 0x11, 0x00, 0x02, 0x11, 0x01, 0x03, 0x11, 0x01,
    0xFF, 0xC4, 0x00, 0x14, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0xFF, 0xC4,
    0x00, 0x14, 0x10, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xDA, 0x00, 0x0C,
    0x03, 0x01, 0x00, 0x02, 0x11, 0x03, 0x11, 0x00, 0x3F, 0x00, 0x00, 0xFF, 0xD9
  ]);
  
  return jpegData;
}

async function uploadTestImage() {
  console.log('🚀 创建并上传测试图片到SFTP服务器\n');
  
  const sftp = new SftpClient();
  
  try {
    // 创建测试图片
    console.log('🎨 创建测试图片...');
    const testImageData = createTestImage();
    const localImagePath = './test-news-image.jpg';
    
    // 保存到本地
    fs.writeFileSync(localImagePath, testImageData);
    console.log(`✅ 测试图片已创建: ${localImagePath} (${testImageData.length} bytes)`);
    
    // 连接到SFTP服务器
    console.log('\n📡 连接到SFTP服务器...');
    await sftp.connect(SFTP_CONFIG);
    console.log('✅ SFTP连接成功!');
    
    // 上传图片到服务器
    const remoteImagePath = 'test-news-image.jpg';
    console.log(`\n📤 上传图片到服务器: ${remoteImagePath}`);
    await sftp.put(localImagePath, remoteImagePath);
    console.log('✅ 图片上传成功!');
    
    // 验证上传
    console.log('\n🔍 验证上传的图片...');
    const uploadedData = await sftp.get(remoteImagePath);
    console.log(`✅ 验证成功! 上传的图片大小: ${uploadedData.length} bytes`);
    
    // 列出文件确认
    console.log('\n📋 列出服务器文件...');
    const list = await sftp.list('./');
    const imageFile = list.find(item => item.name === remoteImagePath);
    if (imageFile) {
      console.log(`✅ 找到上传的图片: ${imageFile.name} (${imageFile.size} bytes)`);
    }
    
    // 清理本地文件
    fs.unlinkSync(localImagePath);
    console.log('🧹 本地临时文件已清理');
    
    return remoteImagePath;
    
  } catch (error) {
    console.error('❌ 上传失败:', error.message);
    throw error;
  } finally {
    try {
      await sftp.end();
      console.log('\n🔌 SFTP连接已关闭');
    } catch (e) {
      console.error('关闭连接时出错:', e.message);
    }
  }
}

async function testImageProxy(imagePath) {
  console.log(`\n🧪 测试图片代理: ${imagePath}`);
  
  try {
    const response = await fetch(`http://localhost:3002/image?path=${encodeURIComponent(imagePath)}`);
    
    if (response.ok) {
      const imageData = await response.arrayBuffer();
      console.log(`✅ 图片代理成功! 大小: ${imageData.byteLength} bytes`);
      console.log(`📊 内容类型: ${response.headers.get('content-type')}`);
      console.log(`💾 缓存状态: ${response.headers.get('x-cache') || 'MISS'}`);
      return true;
    } else {
      const errorData = await response.json();
      console.log(`❌ 图片代理失败:`, errorData);
      return false;
    }
  } catch (error) {
    console.log(`❌ 代理测试失败: ${error.message}`);
    return false;
  }
}

async function main() {
  try {
    // 上传测试图片
    const remoteImagePath = await uploadTestImage();
    
    // 等待一下确保上传完成
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 测试图片代理
    const success = await testImageProxy(remoteImagePath);
    
    if (success) {
      console.log('\n🎉 SFTP图片代理功能测试成功!');
      console.log('\n📝 下一步:');
      console.log('1. 前端已启用SFTP代理功能');
      console.log('2. 可以在新闻页面测试真实图片显示');
      console.log('3. 检查浏览器控制台的图片服务日志');
      console.log(`4. 测试图片路径: ${remoteImagePath}`);
    } else {
      console.log('\n⚠️  图片代理测试失败，请检查代理服务器');
    }
    
  } catch (error) {
    console.error('\n💥 测试过程中发生错误:', error.message);
  }
}

if (require.main === module) {
  main().catch(console.error);
}
