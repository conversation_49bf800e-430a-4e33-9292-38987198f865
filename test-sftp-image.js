#!/usr/bin/env node

/**
 * 测试SFTP图片代理功能
 */

const http = require('http');

// 测试一些可能的图片路径
const testImagePaths = [
  // 尝试一些常见的图片路径
  'test.jpg',
  'image.jpg',
  'photo.png',
  // 尝试一些可能的新闻图片路径
  '/var/www/images/news.jpg',
  '/home/<USER>/images/test.jpg',
  '/tmp/test.jpg',
  // 尝试相对路径
  './test.jpg',
  '../images/test.jpg'
];

async function testSftpImageProxy(imagePath) {
  return new Promise((resolve) => {
    const url = `http://localhost:3002/image?path=${encodeURIComponent(imagePath)}`;
    console.log(`\n🔍 测试图片路径: ${imagePath}`);
    console.log(`📡 请求URL: ${url}`);
    
    const req = http.get(url, (res) => {
      console.log(`📊 响应状态: ${res.statusCode}`);
      console.log(`📋 响应头:`, {
        'content-type': res.headers['content-type'],
        'content-length': res.headers['content-length'],
        'x-cache': res.headers['x-cache']
      });
      
      let data = Buffer.alloc(0);
      res.on('data', (chunk) => {
        data = Buffer.concat([data, chunk]);
      });
      
      res.on('end', () => {
        if (res.statusCode === 200) {
          console.log(`✅ 图片获取成功，大小: ${data.length} bytes`);
          if (res.headers['content-type']) {
            console.log(`🎯 内容类型: ${res.headers['content-type']}`);
          }
          
          // 检查图片头部
          if (data.length > 10) {
            const header = data.slice(0, 10);
            console.log(`📊 图片头部: ${header.toString('hex')}`);
          }
        } else {
          console.log(`❌ 图片获取失败`);
          try {
            const errorData = JSON.parse(data.toString());
            console.log(`📝 错误信息:`, errorData);
          } catch (e) {
            console.log(`📝 原始响应:`, data.toString().substring(0, 200));
          }
        }
        resolve();
      });
    });
    
    req.on('error', (error) => {
      console.log(`❌ 请求失败: ${error.message}`);
      resolve();
    });
    
    req.setTimeout(10000, () => {
      console.log(`⏰ 请求超时`);
      req.destroy();
      resolve();
    });
  });
}

async function testHealthCheck() {
  console.log('🏥 测试SFTP代理服务器健康状态...');
  
  return new Promise((resolve) => {
    const req = http.get('http://localhost:3002/health', (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode === 200) {
          try {
            const healthData = JSON.parse(data);
            console.log('✅ SFTP代理服务器健康状态:', healthData);
          } catch (e) {
            console.log('⚠️  健康检查响应格式异常:', data);
          }
        } else {
          console.log(`❌ 健康检查失败: ${res.statusCode}`);
        }
        resolve();
      });
    });
    
    req.on('error', (error) => {
      console.log(`❌ 健康检查请求失败: ${error.message}`);
      resolve();
    });
  });
}

async function testConnectionEndpoint() {
  console.log('\n🔧 测试SFTP连接端点...');
  
  return new Promise((resolve) => {
    const req = http.get('http://localhost:3002/test-connection', (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode === 200) {
          try {
            const testData = JSON.parse(data);
            console.log('✅ SFTP连接测试成功:', {
              server: testData.server,
              fileCount: testData.fileCount,
              success: testData.success
            });
            
            if (testData.files && testData.files.length > 0) {
              console.log('📁 服务器文件列表:');
              testData.files.forEach((file, index) => {
                const type = file.type === 'd' ? '📁' : '📄';
                console.log(`   ${index + 1}. ${type} ${file.name} (${file.size} bytes)`);
              });
            }
          } catch (e) {
            console.log('⚠️  连接测试响应格式异常:', data);
          }
        } else {
          console.log(`❌ 连接测试失败: ${res.statusCode}`);
          console.log('响应:', data);
        }
        resolve();
      });
    });
    
    req.on('error', (error) => {
      console.log(`❌ 连接测试请求失败: ${error.message}`);
      resolve();
    });
  });
}

async function main() {
  console.log('🚀 SFTP图片代理测试开始\n');
  
  // 测试健康检查
  await testHealthCheck();
  
  // 测试SFTP连接
  await testConnectionEndpoint();
  
  // 测试不同的图片路径
  console.log('\n📸 测试图片路径...');
  for (const imagePath of testImagePaths) {
    await testSftpImageProxy(imagePath);
    await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
  }
  
  console.log('\n✨ 测试完成!');
  console.log('\n💡 提示:');
  console.log('- 如果所有路径都返回404，说明SFTP服务器上可能没有这些测试图片');
  console.log('- 请检查实际的新闻数据中的jpg_path字段内容');
  console.log('- 可以尝试使用真实的图片路径进行测试');
  console.log('- SFTP连接成功表明代理服务器工作正常');
}

if (require.main === module) {
  main().catch(console.error);
}
