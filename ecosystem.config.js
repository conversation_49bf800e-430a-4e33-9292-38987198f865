module.exports = {
  apps: [
    {
      name: 'sftp-image-proxy',
      script: 'sftp-image-proxy-server.prod.js',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        IMAGE_PROXY_PORT: 3002
      },
      env_production: {
        NODE_ENV: 'production',
        IMAGE_PROXY_PORT: 3002
      },
      error_file: './logs/sftp-proxy-error.log',
      out_file: './logs/sftp-proxy-out.log',
      log_file: './logs/sftp-proxy-combined.log',
      time: true,
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      // 健康检查
      health_check_url: 'http://localhost:3002/health',
      health_check_grace_period: 3000,
      // 重启策略
      min_uptime: '10s',
      max_restarts: 10,
      restart_delay: 4000,
      // 监控
      monitoring: false,
      pmx: false
    }
  ],

  deploy: {
    production: {
      user: 'node',
      host: 'your-server.com',
      ref: 'origin/main',
      repo: '**************:your-repo/spacedata-platform.git',
      path: '/var/www/spacedata-platform',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
};
