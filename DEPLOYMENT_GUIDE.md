# 🚀 太空大数据平台部署指南 (包含SFTP图片代理)

## 📋 概述

本指南介绍如何在远程服务器上部署太空大数据平台，包括前端应用和SFTP图片代理服务。

## 🛠️ 部署方式

### 方式一：快速部署 (推荐)

使用增强版的API配置脚本，一键完成所有配置：

```bash
./fix-api-config-with-proxy.sh
```

**执行过程：**
1. 提示输入后端API服务路径
2. 提示输入SFTP图片代理端口 (默认3002)
3. 确认配置信息
4. 自动完成以下操作：
   - 备份原配置文件
   - 更新API配置
   - 更新图片服务配置
   - 安装依赖 (如需要)
   - 构建前端项目
   - 启动SFTP图片代理服务

### 方式二：完整部署

使用完整的部署脚本，包含更多配置选项：

```bash
./deploy-with-image-proxy.sh
```

**功能特性：**
- 完整的系统检查
- 详细的配置选项
- SFTP服务器配置
- PM2进程管理
- 健康检查和监控

## 📦 部署前准备

### 系统要求
- Node.js >= 16.0.0
- npm >= 8.0.0
- 可选：PM2 (用于生产环境进程管理)

### 安装PM2 (推荐)
```bash
npm install -g pm2
```

### 检查依赖
```bash
node --version
npm --version
pm2 --version  # 可选
```

## 🔧 配置说明

### API配置
- **后端API地址**: 您的后端服务器地址，例如 `http://api.example.com:3001`
- **图片代理端口**: SFTP图片代理服务端口，默认 `3002`

### SFTP配置
默认SFTP服务器配置：
- **主机**: 106.119.163.25
- **端口**: 22
- **用户**: yulingjing
- **协议**: SFTP

## 🚀 部署步骤

### 1. 上传项目文件
将项目文件上传到服务器，确保包含以下关键文件：
- `fix-api-config-with-proxy.sh`
- `sftp-image-proxy-server.js`
- `frontend/` 目录
- `package.json`

### 2. 执行部署脚本
```bash
# 进入项目目录
cd /path/to/your/project

# 执行部署脚本
./fix-api-config-with-proxy.sh
```

### 3. 按提示输入配置
```
🌐 请输入您的后端API地址 (例如: http://api.example.com:3001): 
🖼️  请输入SFTP图片代理端口 (默认: 3002): 
确认以上配置并继续? (y/n): y
```

### 4. 等待部署完成
脚本会自动完成所有配置和构建过程。

### 5. 重启Web服务器
```bash
sudo systemctl restart nginx
```

## 📊 服务管理

### 使用PM2管理 (推荐)
```bash
# 查看服务状态
pm2 status

# 查看日志
pm2 logs sftp-image-proxy

# 重启服务
pm2 restart sftp-image-proxy

# 停止服务
pm2 stop sftp-image-proxy
```

### 使用管理脚本
```bash
# 查看服务状态
./manage-services.sh status

# 启动服务
./manage-services.sh start

# 停止服务
./manage-services.sh stop

# 重启服务
./manage-services.sh restart

# 查看日志
./manage-services.sh logs

# 健康检查
./manage-services.sh health
```

### 手动管理
```bash
# 查看日志
tail -f sftp-proxy.log

# 停止服务
kill $(cat .sftp-proxy.pid)
```

## 🔍 验证部署

### 1. 检查服务状态
```bash
# 检查图片代理服务
curl http://localhost:3002/health

# 检查端口监听
netstat -tlnp | grep 3002
```

### 2. 测试功能
- 访问前端应用
- 检查新闻页面图片显示
- 查看浏览器开发者工具网络请求
- 确认API请求正常

### 3. 查看日志
```bash
# PM2日志
pm2 logs sftp-image-proxy

# 或手动日志
tail -f sftp-proxy.log
```

## 🛡️ 安全配置

### 防火墙设置
确保开放必要端口：
```bash
# 开放图片代理端口
sudo ufw allow 3002

# 检查防火墙状态
sudo ufw status
```

### 系统服务 (可选)
安装为系统服务，开机自启动：
```bash
# 安装系统服务
sudo ./manage-services.sh install

# 启动系统服务
sudo systemctl start sftp-image-proxy

# 设置开机自启
sudo systemctl enable sftp-image-proxy
```

## 🔧 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 查看占用端口的进程
lsof -i :3002

# 终止进程
kill -9 <PID>
```

#### 2. SFTP连接失败
```bash
# 测试SFTP连接
node test-sftp-connection.js

# 检查网络连接
ping 106.119.163.25
```

#### 3. 服务启动失败
```bash
# 查看详细日志
pm2 logs sftp-image-proxy --lines 100

# 或查看手动日志
tail -f sftp-proxy.log
```

#### 4. 图片显示问题
- 检查浏览器控制台错误
- 确认图片代理服务运行正常
- 验证SFTP服务器连接

### 日志位置
- **PM2日志**: `~/.pm2/logs/`
- **手动日志**: `./sftp-proxy.log`
- **系统日志**: `sudo journalctl -u sftp-image-proxy`

## 📈 性能优化

### 缓存配置
- **内存缓存**: 24小时，最大200张图片
- **浏览器缓存**: 24小时
- **缓存清理**: 每小时自动清理过期缓存

### 监控建议
```bash
# 查看缓存状态
curl http://localhost:3002/cache/status

# 监控内存使用
pm2 monit

# 查看系统资源
htop
```

## 🔄 更新部署

### 更新代码
```bash
# 拉取最新代码
git pull

# 重新部署
./fix-api-config-with-proxy.sh
```

### 仅重启服务
```bash
# 重启图片代理
./manage-services.sh restart

# 重启Web服务器
sudo systemctl restart nginx
```

## 📞 技术支持

### 健康检查端点
- **服务状态**: `http://your-server:3002/health`
- **缓存状态**: `http://your-server:3002/cache/status`

### 监控指标
- 服务运行状态
- 缓存命中率
- SFTP连接成功率
- 响应时间

---

## 🎯 总结

使用本部署指南，您可以：

1. ✅ **一键部署** - 使用 `fix-api-config-with-proxy.sh` 快速部署
2. ✅ **自动配置** - 自动配置API地址和图片代理
3. ✅ **服务管理** - 使用PM2或管理脚本管理服务
4. ✅ **监控运维** - 完整的健康检查和日志系统
5. ✅ **故障排除** - 详细的故障排除指南

部署完成后，您的太空大数据平台将具备完整的SFTP图片代理功能，为用户提供流畅的图片浏览体验！🚀
